use axum::{extract::State, http::StatusCode, Extension, Json};
use bcrypt::{hash, verify, DEFAULT_COST};
use jsonwebtoken::{encode, Encoding<PERSON><PERSON>, Header};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tracing::{debug, error, info, warn};

use crate::app_state::AppState;
use crate::auth::user_claims::{UserClaims, UserContext};
use crate::config::config_manager::ConfigManager;
use crate::error::error::CocoError;

// 登录请求模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoginRequest {
    pub password: String,
}

// 登录响应模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoginResponse {
    pub access_token: String,
    pub username: String,
    pub id: String,
    pub expire_in: i64,
    pub status: String,
}

// 密码修改请求模型
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ChangePasswordRequest {
    pub old_password: String,
    pub new_password: String,
}

// 密码修改响应模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChangePasswordResponse {
    pub id: String,
    pub result: String,
}

// 统一错误响应模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorResponse {
    pub error: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub message: Option<String>,
}

// 用户信息结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserInfo {
    pub user_id: String,
    pub username: String,
    pub email: String,
    pub roles: Vec<String>,
    pub last_login: Option<String>,
    pub created_at: Option<String>,
    pub preferences: Option<UserPreferences>,
}

// JWT Claims结构已移动到 crate::auth::user_claims 模块

// 默认用户常量
const DEFAULT_USER_LOGIN: &str = "coco-default-user";
const DEFAULT_USER_PASSWORD_KEY: &str = "default_user_password";
const DEFAULT_JWT_SECRET: &str = "coco-server-jwt-secret-key-change-in-production";

pub async fn login_handler(
    State(config_manager): State<Arc<ConfigManager>>,
    Json(payload): Json<LoginRequest>,
) -> Result<Json<LoginResponse>, (StatusCode, Json<ErrorResponse>)> {
    info!("Handling /account/login request");

    // 验证密码
    match verify_password(&config_manager, &payload.password).await {
        Ok(is_valid) => {
            if is_valid {
                // 获取用户信息
                match get_user_info().await {
                    Ok(user_info) => {
                        // 生成JWT访问令牌
                        match generate_jwt_token(&user_info).await {
                            Ok(response) => {
                                info!("Login successful, returning response");
                                // 测试序列化
                                match serde_json::to_string(&response) {
                                    Ok(json_str) => {
                                        info!("Response serialized successfully: {}", json_str);
                                    }
                                    Err(e) => {
                                        error!("Failed to serialize response: {}", e);
                                        return Err((
                                            StatusCode::INTERNAL_SERVER_ERROR,
                                            Json(ErrorResponse {
                                                error: "Failed to serialize response".to_string(),
                                                message: Some(e.to_string()),
                                            }),
                                        ));
                                    }
                                }
                                info!("About to return JSON response");
                                Ok(Json(response))
                            }
                            Err(e) => {
                                error!("Failed to generate JWT token: {}", e);
                                Err((
                                    StatusCode::INTERNAL_SERVER_ERROR,
                                    Json(ErrorResponse {
                                        error: "Failed to generate access token".to_string(),
                                        message: Some(format!(
                                            "JWT token generation failed: {}",
                                            e
                                        )),
                                    }),
                                ))
                            }
                        }
                    }
                    Err(e) => {
                        error!("Failed to get user info: {}", e);
                        Err((
                            StatusCode::INTERNAL_SERVER_ERROR,
                            Json(ErrorResponse {
                                error: "Failed to get user info".to_string(),
                                message: Some(format!("User info retrieval failed: {}", e)),
                            }),
                        ))
                    }
                }
            } else {
                info!("Login failed: invalid password");
                Err((
                    StatusCode::FORBIDDEN,
                    Json(ErrorResponse {
                        error: "failed to login".to_string(),
                        message: Some("Invalid password provided".to_string()),
                    }),
                ))
            }
        }
        Err(e) => {
            error!("Password verification failed: {}", e);
            Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Password verification failed".to_string(),
                    message: Some(format!(
                        "Internal error during password verification: {}",
                        e
                    )),
                }),
            ))
        }
    }
}

async fn verify_password(
    config_manager: &ConfigManager,
    password: &str,
) -> Result<bool, CocoError> {
    info!("Starting password verification for password: {}", password);

    // 获取保存的密码哈希
    let saved_hash = get_saved_password_hash(config_manager).await?;
    info!("Retrieved password hash: {}", saved_hash);

    // 验证密码
    match verify(password, &saved_hash) {
        Ok(is_valid) => {
            info!("Password verification completed. Result: {}", is_valid);
            Ok(is_valid)
        }
        Err(e) => {
            error!("Password verification error: {}", e);
            Err(CocoError::auth("Password verification failed"))
        }
    }
}

async fn get_saved_password_hash(_config_manager: &ConfigManager) -> Result<String, CocoError> {
    use std::fs;

    // 首先检查环境变量中的初始密码（向后兼容）
    if let Ok(initial_password) = std::env::var("EASYSEARCH_INITIAL_ADMIN_PASSWORD") {
        if !initial_password.is_empty() {
            debug!("Using initial admin password from environment variable");
            // 对初始密码进行哈希处理并返回
            return hash_password(&initial_password).await;
        }
    }

    // 尝试从.password文件中读取保存的密码哈希
    match fs::read_to_string(".password") {
        Ok(hashed_password) => {
            debug!("Using password hash from .password file");
            Ok(hashed_password.trim().to_string())
        }
        Err(e) => {
            error!("Failed to read .password file: {}", e);
            // 如果文件不存在，使用默认密码（仅用于开发环境）
            warn!("No password file found, using default password for development");
            let default_password = "coco123"; // 开发环境默认密码
            hash_password(default_password).await
        }
    }
}

async fn hash_password(password: &str) -> Result<String, CocoError> {
    match hash(password, DEFAULT_COST) {
        Ok(hashed) => Ok(hashed),
        Err(e) => {
            error!("Password hashing error: {}", e);
            Err(CocoError::server("Failed to hash password"))
        }
    }
}

/// 保存新密码到文件
async fn save_password(password: &str) -> Result<(), CocoError> {
    use std::fs;

    // 生成密码哈希
    let hashed_password = hash_password(password).await?;

    // 保存到.password文件
    match fs::write(".password", hashed_password) {
        Ok(_) => {
            info!("Password hash saved to .password file");
            Ok(())
        }
        Err(e) => {
            error!("Failed to write password file: {}", e);
            Err(CocoError::server("Failed to save password"))
        }
    }
}

pub async fn get_user_info() -> Result<UserInfo, CocoError> {
    use serde_json::Value;
    use std::fs;

    // 尝试从.user_profile文件中读取用户信息
    match fs::read_to_string(".user_profile") {
        Ok(profile_content) => {
            debug!("Using user profile from .user_profile file");
            match serde_json::from_str::<Value>(&profile_content) {
                Ok(profile_json) => {
                    let user_info = UserInfo {
                        user_id: profile_json
                            .get("email")
                            .and_then(|v| v.as_str())
                            .unwrap_or(DEFAULT_USER_LOGIN)
                            .to_string(),
                        username: profile_json
                            .get("name")
                            .and_then(|v| v.as_str())
                            .unwrap_or(DEFAULT_USER_LOGIN)
                            .to_string(),
                        email: profile_json
                            .get("email")
                            .and_then(|v| v.as_str())
                            .unwrap_or("")
                            .to_string(),
                        roles: vec!["admin".to_string()],
                        last_login: Some(chrono::Utc::now().to_rfc3339()), // 设置当前时间为最后登录时间
                        created_at: profile_json
                            .get("created_at")
                            .and_then(|v| v.as_str())
                            .map(|s| s.to_string()),
                        preferences: None, // 可以后续从配置中读取
                    };
                    Ok(user_info)
                }
                Err(e) => {
                    error!("Failed to parse user profile JSON: {}", e);
                    // 使用默认用户信息
                    Ok(get_default_user_info())
                }
            }
        }
        Err(e) => {
            debug!("No user profile file found: {}", e);
            // 使用默认用户信息
            Ok(get_default_user_info())
        }
    }
}

fn get_default_user_info() -> UserInfo {
    UserInfo {
        user_id: DEFAULT_USER_LOGIN.to_string(),
        username: DEFAULT_USER_LOGIN.to_string(),
        email: "".to_string(),
        roles: vec!["admin".to_string()],
        last_login: Some(chrono::Utc::now().to_rfc3339()), // 设置当前时间为最后登录时间
        created_at: Some(chrono::Utc::now().to_rfc3339()), // 设置当前时间为创建时间
        preferences: None,                                 // 使用默认偏好设置
    }
}

/// 根据用户ID获取用户信息 - 为多用户支持做准备
pub async fn get_user_info_by_id(user_id: &str) -> Result<UserInfo, CocoError> {
    // 目前只支持默认用户，将来可以扩展为从数据库查询
    if user_id == DEFAULT_USER_LOGIN || user_id.is_empty() {
        get_user_info().await
    } else {
        // 为将来的多用户支持预留接口
        warn!("Multi-user support not implemented yet, falling back to default user");
        get_user_info().await
    }
}

/// 更新用户最后登录时间
pub async fn update_user_last_login(user_id: &str) -> Result<(), CocoError> {
    // 目前只记录日志，将来可以扩展为更新数据库
    info!(
        "User {} logged in at {}",
        user_id,
        chrono::Utc::now().to_rfc3339()
    );
    // TODO: 实现数据库更新逻辑
    Ok(())
}

/// 保存用户偏好设置
pub async fn save_user_preferences(
    user_id: &str,
    preferences: &UserPreferences,
) -> Result<(), CocoError> {
    // 目前只记录日志，将来可以扩展为保存到数据库
    info!("Saving preferences for user {}: {:?}", user_id, preferences);
    // TODO: 实现偏好设置持久化逻辑
    Ok(())
}

/// 获取JWT密钥，优先从环境变量获取，否则使用默认值
fn get_jwt_secret() -> String {
    std::env::var("JWT_SECRET").unwrap_or_else(|_| {
        warn!("JWT_SECRET environment variable not set, using default secret");
        DEFAULT_JWT_SECRET.to_string()
    })
}

// 用户配置文件响应模型 - 符合API规格要求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserProfileResponse {
    // API规格要求的核心字段
    pub id: String,
    pub username: String,           // API规格要求的字段
    pub roles: Vec<String>,         // API规格要求的字段
    pub last_login: Option<String>, // API规格要求的字段

    // 客户端兼容性字段
    #[serde(skip_serializing_if = "Option::is_none")]
    pub name: Option<String>, // 保持与Tauri应用的兼容性
    #[serde(skip_serializing_if = "Option::is_none")]
    pub email: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub avatar: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub preferences: Option<UserPreferences>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub created_at: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub language: Option<String>,
}

// 用户偏好设置 - 扩展支持更多设置选项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserPreferences {
    pub theme: Option<String>,       // 主题设置 (light, dark, auto)
    pub language: Option<String>,    // 语言设置 (zh-CN, en-US, etc.)
    pub timezone: Option<String>,    // 时区设置
    pub date_format: Option<String>, // 日期格式偏好
    pub notifications: Option<NotificationPreferences>, // 通知偏好
}

// 通知偏好设置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationPreferences {
    pub email_notifications: Option<bool>,
    pub push_notifications: Option<bool>,
    pub sound_enabled: Option<bool>,
}

/// 获取用户配置文件处理器
pub async fn profile_handler(
    State(_config_manager): State<Arc<ConfigManager>>,
) -> Result<Json<UserProfileResponse>, (StatusCode, Json<ErrorResponse>)> {
    info!("Handling /account/profile request");

    // 获取用户信息
    match get_user_info().await {
        Ok(user_info) => {
            // 构建符合API规格的用户资料响应
            let profile = UserProfileResponse {
                // API规格要求的核心字段
                id: user_info.user_id.clone(),
                username: user_info.username.clone(), // API规格要求的字段
                roles: user_info.roles.clone(),       // API规格要求的字段
                last_login: user_info.last_login.clone(), // API规格要求的字段

                // 客户端兼容性字段
                name: Some(user_info.username.clone()), // 保持与Tauri应用的兼容性
                email: Some(user_info.email.clone()),
                avatar: None, // 可以后续从配置中读取
                preferences: user_info.preferences.clone().or_else(|| {
                    // 提供默认偏好设置
                    Some(UserPreferences {
                        theme: Some("light".to_string()),
                        language: Some("zh-CN".to_string()),
                        timezone: Some("Asia/Shanghai".to_string()),
                        date_format: Some("YYYY-MM-DD".to_string()),
                        notifications: Some(NotificationPreferences {
                            email_notifications: Some(true),
                            push_notifications: Some(true),
                            sound_enabled: Some(true),
                        }),
                    })
                }),
                created_at: user_info.created_at.clone(),
                language: Some("zh-CN".to_string()),
            };

            // 添加调试日志，查看实际返回的JSON
            match serde_json::to_string(&profile) {
                Ok(json_str) => {
                    info!("Profile response JSON: {}", json_str);
                }
                Err(e) => {
                    error!("Failed to serialize profile to JSON: {}", e);
                }
            }

            Ok(Json(profile))
        }
        Err(e) => {
            error!("Failed to get user info: {}", e);
            Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Failed to get user profile".to_string(),
                    message: Some(format!("User profile retrieval failed: {}", e)),
                }),
            ))
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::json;
    use std::fs;
    use tempfile::NamedTempFile;

    #[tokio::test]
    async fn test_get_default_user_info() {
        let user_info = get_default_user_info();

        assert_eq!(user_info.user_id, DEFAULT_USER_LOGIN);
        assert_eq!(user_info.username, DEFAULT_USER_LOGIN);
        assert_eq!(user_info.roles, vec!["admin"]);
        assert!(user_info.last_login.is_some());
        assert!(user_info.created_at.is_some());
    }

    #[tokio::test]
    async fn test_get_user_info_with_profile_file() {
        // 创建临时用户配置文件
        let profile_data = json!({
            "name": "测试用户",
            "email": "<EMAIL>",
            "created_at": "2024-01-01T00:00:00Z"
        });

        // 保存到临时文件
        let temp_file = NamedTempFile::new().unwrap();
        fs::write(temp_file.path(), profile_data.to_string()).unwrap();

        // 备份原始文件（如果存在）
        let backup_exists = std::path::Path::new(".user_profile").exists();
        let backup_content = if backup_exists {
            Some(fs::read_to_string(".user_profile").unwrap())
        } else {
            None
        };

        // 复制临时文件到.user_profile
        fs::copy(temp_file.path(), ".user_profile").unwrap();

        // 测试获取用户信息
        let user_info = get_user_info().await.unwrap();

        assert_eq!(user_info.username, "测试用户");
        assert_eq!(user_info.email, "<EMAIL>");
        assert_eq!(user_info.user_id, "<EMAIL>");
        assert_eq!(user_info.roles, vec!["admin"]);
        assert!(user_info.last_login.is_some());

        // 恢复原始文件
        if let Some(content) = backup_content {
            fs::write(".user_profile", content).unwrap();
        } else {
            let _ = fs::remove_file(".user_profile");
        }
    }

    #[tokio::test]
    async fn test_get_user_info_by_id() {
        // 测试默认用户ID
        let user_info = get_user_info_by_id(DEFAULT_USER_LOGIN).await.unwrap();
        // 注意：实际的user_id可能来自配置文件，所以我们只检查函数能正常执行
        assert!(!user_info.user_id.is_empty());
        assert!(!user_info.username.is_empty());

        // 测试空用户ID
        let user_info = get_user_info_by_id("").await.unwrap();
        assert!(!user_info.user_id.is_empty());

        // 测试其他用户ID（应该回退到默认用户）
        let user_info = get_user_info_by_id("other-user").await.unwrap();
        assert!(!user_info.user_id.is_empty());
    }

    #[tokio::test]
    async fn test_update_user_last_login() {
        // 测试更新最后登录时间（目前只记录日志）
        let result = update_user_last_login("test-user").await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_save_user_preferences() {
        let preferences = UserPreferences {
            theme: Some("dark".to_string()),
            language: Some("en-US".to_string()),
            timezone: Some("UTC".to_string()),
            date_format: Some("MM/DD/YYYY".to_string()),
            notifications: Some(NotificationPreferences {
                email_notifications: Some(false),
                push_notifications: Some(true),
                sound_enabled: Some(false),
            }),
        };

        // 测试保存用户偏好设置（目前只记录日志）
        let result = save_user_preferences("test-user", &preferences).await;
        assert!(result.is_ok());
    }

    #[test]
    fn test_user_profile_response_serialization() {
        let profile = UserProfileResponse {
            // API规格要求的核心字段
            id: "test-user".to_string(),
            username: "test-user".to_string(),
            roles: vec!["admin".to_string()],
            last_login: Some("2024-01-15T10:30:00Z".to_string()),

            // 客户端兼容性字段
            name: Some("测试用户".to_string()),
            email: Some("<EMAIL>".to_string()),
            avatar: None,
            preferences: Some(UserPreferences {
                theme: Some("light".to_string()),
                language: Some("zh-CN".to_string()),
                timezone: Some("Asia/Shanghai".to_string()),
                date_format: Some("YYYY-MM-DD".to_string()),
                notifications: Some(NotificationPreferences {
                    email_notifications: Some(true),
                    push_notifications: Some(true),
                    sound_enabled: Some(true),
                }),
            }),
            created_at: Some("2024-01-01T00:00:00Z".to_string()),
            language: Some("zh-CN".to_string()),
        };

        // 测试序列化
        let json_str = serde_json::to_string(&profile).unwrap();
        let parsed: serde_json::Value = serde_json::from_str(&json_str).unwrap();

        // 验证API规格要求的字段存在
        assert!(parsed.get("id").is_some());
        assert!(parsed.get("username").is_some());
        assert!(parsed.get("roles").is_some());
        assert!(parsed.get("last_login").is_some());

        // 验证客户端兼容性字段存在
        assert!(parsed.get("name").is_some());
        assert!(parsed.get("email").is_some());
        assert!(parsed.get("preferences").is_some());
    }

    #[test]
    fn test_user_preferences_serialization() {
        let preferences = UserPreferences {
            theme: Some("dark".to_string()),
            language: Some("en-US".to_string()),
            timezone: Some("UTC".to_string()),
            date_format: Some("MM/DD/YYYY".to_string()),
            notifications: Some(NotificationPreferences {
                email_notifications: Some(false),
                push_notifications: Some(true),
                sound_enabled: Some(false),
            }),
        };

        // 测试序列化和反序列化
        let json_str = serde_json::to_string(&preferences).unwrap();
        let parsed: UserPreferences = serde_json::from_str(&json_str).unwrap();

        assert_eq!(parsed.theme, Some("dark".to_string()));
        assert_eq!(parsed.language, Some("en-US".to_string()));
        assert_eq!(parsed.timezone, Some("UTC".to_string()));
        assert_eq!(parsed.date_format, Some("MM/DD/YYYY".to_string()));
        assert!(parsed.notifications.is_some());

        let notifications = parsed.notifications.unwrap();
        assert_eq!(notifications.email_notifications, Some(false));
        assert_eq!(notifications.push_notifications, Some(true));
        assert_eq!(notifications.sound_enabled, Some(false));
    }
}

pub async fn generate_jwt_token(user_info: &UserInfo) -> Result<LoginResponse, CocoError> {
    use std::time::{SystemTime, UNIX_EPOCH};

    info!(
        "Starting JWT token generation for user: {}",
        user_info.username
    );

    let now = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .expect("Time went backwards")
        .as_secs() as usize;

    let expire_in = now as i64 + 86400; // 24小时

    let claims = UserClaims::new(
        user_info.user_id.clone(),
        user_info.username.clone(),
        user_info.roles.clone(),
        "simple".to_string(), // provider
        now + 86400,          // exp: 24小时过期
        now,                  // iat: 当前时间
    );

    info!("Created JWT claims for user: {}", user_info.username);

    // 从配置管理器获取JWT密钥
    let secret = get_jwt_secret();
    let token = encode(
        &Header::default(),
        &claims,
        &EncodingKey::from_secret(secret.as_ref()),
    )
    .map_err(|e| CocoError::server(&format!("Failed to generate JWT token: {}", e)))?;

    info!(
        "Successfully generated JWT token for user: {}",
        user_info.username
    );

    let response = LoginResponse {
        access_token: token,
        username: user_info.username.clone(),
        id: user_info.user_id.clone(),
        expire_in,
        status: "ok".to_string(),
    };

    info!("Created login response for user: {}", user_info.username);
    Ok(response)
}

/// 密码修改处理器
/// 处理 PUT /account/password 请求
pub async fn change_password_handler(
    State(config_manager): State<Arc<ConfigManager>>,
    Json(payload): Json<ChangePasswordRequest>,
) -> Result<Json<ChangePasswordResponse>, (StatusCode, Json<ErrorResponse>)> {
    info!("Handling PUT /account/password request");

    // 验证请求参数
    if payload.old_password.is_empty() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "old_password is required".to_string(),
                message: Some("Old password cannot be empty".to_string()),
            }),
        ));
    }

    if payload.new_password.is_empty() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "new_password is required".to_string(),
                message: Some("New password cannot be empty".to_string()),
            }),
        ));
    }

    // 验证新密码强度（可选）
    if payload.new_password.len() < 6 {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "new_password too weak".to_string(),
                message: Some("New password must be at least 6 characters long".to_string()),
            }),
        ));
    }

    // 验证旧密码
    match verify_password(&config_manager, &payload.old_password).await {
        Ok(is_valid) => {
            if !is_valid {
                info!("Password change failed: invalid old password");
                return Err((
                    StatusCode::FORBIDDEN,
                    Json(ErrorResponse {
                        error: "failed to login".to_string(),
                        message: Some("Old password is incorrect".to_string()),
                    }),
                ));
            }
        }
        Err(e) => {
            error!("Old password verification failed: {}", e);
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Password verification failed".to_string(),
                    message: Some(format!("Failed to verify old password: {}", e)),
                }),
            ));
        }
    }

    // 保存新密码
    match save_password(&payload.new_password).await {
        Ok(_) => {
            info!("Password changed successfully");
            let user_info = get_user_info()
                .await
                .unwrap_or_else(|_| get_default_user_info());
            Ok(Json(ChangePasswordResponse {
                id: user_info.user_id,
                result: "updated".to_string(),
            }))
        }
        Err(e) => {
            error!("Failed to save new password: {}", e);
            Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Failed to save password".to_string(),
                    message: Some(format!("Password save operation failed: {}", e)),
                }),
            ))
        }
    }
}

#[tokio::test]
async fn test_hash_password() {
    let password = "test_password";
    let result = hash_password(password).await;
    assert!(result.is_ok());

    let hashed = result.unwrap();
    assert!(!hashed.is_empty());
    assert_ne!(hashed, password);

    // 验证哈希后的密码可以被验证
    let verify_result = verify(password, &hashed);
    assert!(verify_result.is_ok());
    assert!(verify_result.unwrap());
}

// 注意：save_password测试需要文件系统操作，暂时跳过

#[tokio::test]
async fn test_get_jwt_secret() {
    // 测试默认密钥
    let secret = get_jwt_secret();
    assert!(!secret.is_empty());
    assert_eq!(secret, DEFAULT_JWT_SECRET);

    // 测试环境变量
    std::env::set_var("JWT_SECRET", "test-secret");
    let secret = get_jwt_secret();
    assert_eq!(secret, "test-secret");

    // 清理环境变量
    std::env::remove_var("JWT_SECRET");
}

#[tokio::test]
async fn test_get_default_user_info() {
    let user_info = get_default_user_info();
    assert_eq!(user_info.user_id, DEFAULT_USER_LOGIN);
    assert_eq!(user_info.username, DEFAULT_USER_LOGIN);
    assert!(user_info.roles.contains(&"admin".to_string()));
}

#[test]
fn test_login_request_serialization() {
    let request = LoginRequest {
        password: "test_password".to_string(),
    };

    let json = serde_json::to_string(&request).unwrap();
    assert!(json.contains("test_password"));

    let deserialized: LoginRequest = serde_json::from_str(&json).unwrap();
    assert_eq!(deserialized.password, "test_password");
}

#[test]
fn test_change_password_request_serialization() {
    let request = ChangePasswordRequest {
        old_password: "old_pass".to_string(),
        new_password: "new_pass".to_string(),
    };

    let json = serde_json::to_string(&request).unwrap();
    assert!(json.contains("old_pass"));
    assert!(json.contains("new_pass"));

    let deserialized: ChangePasswordRequest = serde_json::from_str(&json).unwrap();
    assert_eq!(deserialized.old_password, "old_pass");
    assert_eq!(deserialized.new_password, "new_pass");
}

#[test]
fn test_error_response_serialization() {
    let error = ErrorResponse {
        error: "test_error".to_string(),
        message: Some("test_message".to_string()),
    };

    let json = serde_json::to_string(&error).unwrap();
    assert!(json.contains("test_error"));
    assert!(json.contains("test_message"));

    let error_without_message = ErrorResponse {
        error: "test_error".to_string(),
        message: None,
    };

    let json = serde_json::to_string(&error_without_message).unwrap();
    assert!(json.contains("test_error"));
    assert!(!json.contains("message"));
}

// 注销请求模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogoutRequest {
    // 注销请求目前不需要额外参数，令牌从认证中间件获取
}

// 注销响应模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogoutResponse {
    pub status: String,
    pub message: String,
}

/// 注销处理器
/// 处理 POST /account/logout 请求
/// 将当前用户的令牌添加到黑名单，实现注销功能
pub async fn logout_handler(
    State(app_state): State<AppState>,
    Extension(user_context): Extension<UserContext>,
) -> Result<Json<LogoutResponse>, (StatusCode, Json<ErrorResponse>)> {
    info!(
        "Handling POST /account/logout request for user: {}",
        user_context.username
    );

    // 从请求头中提取令牌（这需要在认证中间件中传递）
    // 由于我们已经通过了认证中间件，用户上下文是有效的
    // 但我们需要获取原始令牌来添加到黑名单

    // 注意：这里我们需要修改认证中间件来传递原始令牌
    // 目前先实现基本的注销逻辑

    match user_context.auth_type {
        crate::auth::user_claims::AuthType::JWT => {
            // 对于JWT令牌，将其添加到黑名单
            if let Some(ref original_token) = user_context.original_token {
                info!(
                    "Adding JWT token to blacklist for user: {}",
                    user_context.user_id
                );
                app_state.token_blacklist.add_token(original_token).await;
                info!("JWT token successfully added to blacklist");
            } else {
                warn!("No original token found in user context for JWT logout");
                return Err((
                    StatusCode::INTERNAL_SERVER_ERROR,
                    Json(ErrorResponse {
                        error: "Internal error".to_string(),
                        message: Some("Unable to process logout request".to_string()),
                    }),
                ));
            }
        }
        crate::auth::user_claims::AuthType::ApiToken => {
            // 对于API令牌，撤销令牌
            if let Some(ref original_token) = user_context.original_token {
                info!("Revoking API token for user: {}", user_context.user_id);

                // 通过令牌查找并撤销
                match app_state
                    .token_service
                    .validate_api_token(original_token)
                    .await
                {
                    Ok(access_token) => {
                        if let Err(e) = app_state
                            .token_service
                            .revoke_token(&access_token.id, &user_context.user_id)
                            .await
                        {
                            error!("Failed to revoke API token: {}", e);
                            return Err((
                                StatusCode::INTERNAL_SERVER_ERROR,
                                Json(ErrorResponse {
                                    error: "Failed to revoke token".to_string(),
                                    message: Some(format!("Token revocation failed: {}", e)),
                                }),
                            ));
                        }
                        info!("API token successfully revoked");
                    }
                    Err(e) => {
                        warn!("API token validation failed during logout: {}", e);
                        // 即使验证失败，我们也认为注销成功，因为令牌可能已经无效
                    }
                }
            } else {
                warn!("No original token found in user context for API token logout");
                return Err((
                    StatusCode::INTERNAL_SERVER_ERROR,
                    Json(ErrorResponse {
                        error: "Internal error".to_string(),
                        message: Some("Unable to process logout request".to_string()),
                    }),
                ));
            }
        }
    }

    // 记录注销事件
    info!("User {} logged out successfully", user_context.user_id);

    let response = LogoutResponse {
        status: "ok".to_string(),
        message: "Logged out successfully".to_string(),
    };

    Ok(Json(response))
}

#[cfg(test)]
mod logout_tests {
    use super::*;
    use crate::auth::token_blacklist::TokenBlacklist;
    use crate::auth::user_claims::{AuthType, UserContext};
    use crate::services::token_service::TokenService;
    use std::sync::Arc;

    #[tokio::test]
    async fn test_logout_request_serialization() {
        let request = LogoutRequest {};
        let json = serde_json::to_string(&request).unwrap();
        assert!(json.contains("{}"));

        let deserialized: LogoutRequest = serde_json::from_str(&json).unwrap();
        // LogoutRequest 是空结构体，所以没有字段可以验证
        let _ = deserialized;
    }

    #[test]
    fn test_logout_response_serialization() {
        let response = LogoutResponse {
            status: "ok".to_string(),
            message: "Logged out successfully".to_string(),
        };

        let json = serde_json::to_string(&response).unwrap();
        assert!(json.contains("ok"));
        assert!(json.contains("Logged out successfully"));

        let deserialized: LogoutResponse = serde_json::from_str(&json).unwrap();
        assert_eq!(deserialized.status, "ok");
        assert_eq!(deserialized.message, "Logged out successfully");
    }

    #[tokio::test]
    async fn test_jwt_token_blacklist_integration() {
        let blacklist = TokenBlacklist::new();
        let test_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.signature";

        // 初始状态令牌不在黑名单中
        assert!(!blacklist.is_blacklisted(test_token).await);

        // 添加到黑名单
        blacklist.add_token(test_token).await;

        // 现在应该在黑名单中
        assert!(blacklist.is_blacklisted(test_token).await);
    }

    #[tokio::test]
    #[ignore] // 需要运行中的SurrealDB实例
    async fn test_api_token_revocation() {
        // 创建测试用的TokenService需要TokenRepository
        use crate::database::{DatabaseConfig, SurrealDBClient};
        use crate::repositories::token_repository::TokenRepository;

        let db_config = DatabaseConfig::default();
        let db_client = Arc::new(SurrealDBClient::new(db_config).await.unwrap());
        let token_repository = Arc::new(TokenRepository::new_with_global_db());
        let token_service = TokenService::new(token_repository);
        let user_id = "test-user";
        let token_name = Some("test-token".to_string());

        // 生成API令牌
        let token = token_service
            .generate_api_token(user_id, token_name)
            .await
            .unwrap();

        // 验证令牌有效
        let validated = token_service
            .validate_api_token(&token.access_token)
            .await
            .unwrap();
        assert_eq!(validated.id, token.id);

        // 撤销令牌
        token_service
            .revoke_token(&token.id, user_id)
            .await
            .unwrap();

        // 验证令牌已被撤销
        let result = token_service.validate_api_token(&token.access_token).await;
        assert!(result.is_err());
    }

    #[test]
    fn test_user_context_with_original_token() {
        let claims = UserClaims::new(
            "test-user".to_string(),
            "test-user".to_string(),
            vec!["admin".to_string()],
            "simple".to_string(),
            1234567890,
            1234567890,
        );

        let original_token = "test-jwt-token";
        let user_context = UserContext::from_jwt_claims(&claims, Some(original_token.to_string()));

        assert_eq!(user_context.user_id, "test-user");
        assert_eq!(user_context.username, "test-user");
        assert_eq!(user_context.auth_type, AuthType::JWT);
        assert_eq!(
            user_context.original_token,
            Some(original_token.to_string())
        );
    }
}
