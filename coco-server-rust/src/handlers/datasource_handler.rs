use crate::config::config_manager::ConfigManager;
use crate::models::datasource::*;
use crate::models::document::*;
use crate::services::datasource_service::DataSourceService;
use axum::{
    body::Bytes,
    extract::{Path, Query, State},
    http::{HeaderMap, StatusCode},
    response::Json,
};
use serde_json::{json, Value};
use std::collections::HashMap;
use std::sync::Arc;
use tracing::{error, info, warn};

/// 应用状态，包含所有服务依赖
#[derive(Clone)]
pub struct AppState {
    pub config_manager: Arc<ConfigManager>,
    pub datasource_service: Option<Arc<DataSourceService>>,
}

/// 创建数据源处理器
///
/// 处理 POST /datasource/ 请求
pub async fn create_datasource_handler(
    State(app_state): State<AppState>,
    Json(request): Json<CreateDataSourceRequest>,
) -> Result<Json<CreateDataSourceResponse>, StatusCode> {
    info!("处理创建数据源请求: name={}", request.name);

    // 获取数据源服务
    let datasource_service = match &app_state.datasource_service {
        Some(service) => service,
        None => {
            error!("数据源服务未初始化");
            return Err(StatusCode::INTERNAL_SERVER_ERROR);
        }
    };

    // 调用业务服务创建数据源
    match datasource_service.create_datasource(request).await {
        Ok(id) => {
            let response = CreateDataSourceResponse {
                id,
                result: "created".to_string(),
            };
            Ok(Json(response))
        }
        Err(e) => {
            error!("创建数据源失败: {}", e);
            Err(StatusCode::BAD_REQUEST)
        }
    }
}

/// 获取数据源处理器
///
/// 处理 GET /datasource/:id 请求
pub async fn get_datasource_handler(
    State(app_state): State<AppState>,
    Path(id): Path<String>,
) -> Result<Json<GetDataSourceResponse>, StatusCode> {
    info!("处理获取数据源请求: id={}", id);

    // 获取数据源服务
    let datasource_service = match &app_state.datasource_service {
        Some(service) => service,
        None => {
            error!("数据源服务未初始化");
            return Err(StatusCode::INTERNAL_SERVER_ERROR);
        }
    };

    // 调用业务服务获取数据源
    match datasource_service.get_datasource(&id).await {
        Ok(Some(datasource)) => {
            let response = GetDataSourceResponse {
                found: true,
                id: id.clone(),
                source: datasource,
            };
            Ok(Json(response))
        }
        Ok(None) => Err(StatusCode::NOT_FOUND),
        Err(e) => {
            error!("获取数据源失败: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// 更新数据源处理器
///
/// 处理 PUT /datasource/:id 请求
pub async fn update_datasource_handler(
    State(app_state): State<AppState>,
    Path(id): Path<String>,
    Json(request): Json<UpdateDataSourceRequest>,
) -> Result<Json<UpdateDataSourceResponse>, StatusCode> {
    info!("处理更新数据源请求: id={}", id);

    // 获取数据源服务
    let datasource_service = match &app_state.datasource_service {
        Some(service) => service,
        None => {
            error!("数据源服务未初始化");
            return Err(StatusCode::INTERNAL_SERVER_ERROR);
        }
    };

    // 调用业务服务更新数据源
    match datasource_service.update_datasource(&id, request).await {
        Ok(()) => {
            let response = UpdateDataSourceResponse {
                id,
                result: "updated".to_string(),
            };
            Ok(Json(response))
        }
        Err(e) => {
            error!("更新数据源失败: {}", e);
            Err(StatusCode::BAD_REQUEST)
        }
    }
}

/// 删除数据源处理器
///
/// 处理 DELETE /datasource/:id 请求
pub async fn delete_datasource_handler(
    State(app_state): State<AppState>,
    Path(id): Path<String>,
) -> Result<Json<DeleteDataSourceResponse>, StatusCode> {
    info!("处理删除数据源请求: id={}", id);

    // 获取数据源服务
    let datasource_service = match &app_state.datasource_service {
        Some(service) => service,
        None => {
            error!("数据源服务未初始化");
            return Err(StatusCode::INTERNAL_SERVER_ERROR);
        }
    };

    // 调用业务服务删除数据源
    match datasource_service.delete_datasource(&id).await {
        Ok(()) => {
            let response = DeleteDataSourceResponse {
                id,
                result: "deleted".to_string(),
            };
            Ok(Json(response))
        }
        Err(e) => {
            error!("删除数据源失败: {}", e);
            Err(StatusCode::BAD_REQUEST)
        }
    }
}

/// 处理 GET /datasource/_search 请求（兼容版本）
pub async fn search_datasource_get(
    Query(params): Query<HashMap<String, String>>,
    State(config_manager): State<Arc<ConfigManager>>,
) -> Result<Json<SearchResponse>, StatusCode> {
    info!("处理GET搜索数据源请求: params={:?}", params);

    // 临时返回模拟数据，直到完整的服务架构初始化完成
    let mock_datasources = vec![
        DataSource {
            id: Some("ds_001".to_string()),
            r#type: "connector".to_string(),
            name: "Sample DataSource 1".to_string(),
            description: Some("A sample data source for testing".to_string()),
            icon: None,
            category: Some("database".to_string()),
            tags: None,
            connector: Some(crate::models::connector::ConnectorConfig {
                id: "hugo_site".to_string(),
                config: Some(json!({"host": "localhost", "port": 5432})),
            }),
            sync_enabled: true,
            enabled: true,
            created: Some(chrono::Utc::now()),
            updated: Some(chrono::Utc::now()),
        },
        DataSource {
            id: Some("ds_002".to_string()),
            r#type: "connector".to_string(),
            name: "Sample DataSource 2".to_string(),
            description: Some("Another sample data source".to_string()),
            icon: None,
            category: Some("file".to_string()),
            tags: None,
            connector: Some(crate::models::connector::ConnectorConfig {
                id: "file_system".to_string(),
                config: Some(json!({"path": "/data/files"})),
            }),
            sync_enabled: true,
            enabled: true,
            created: Some(chrono::Utc::now()),
            updated: Some(chrono::Utc::now()),
        },
    ];

    let hits: Vec<SearchHit<DataSource>> = mock_datasources
        .into_iter()
        .enumerate()
        .map(|(i, ds)| SearchHit {
            _index: "datasource".to_string(),
            _type: "_doc".to_string(),
            _id: ds.id.clone().unwrap_or_default(),
            _score: Some(1.0 - (i as f64 * 0.1)),
            _source: ds,
        })
        .collect();

    let response = SearchResponse {
        took: 5,
        timed_out: false,
        hits: SearchHits {
            total: SearchTotal {
                value: hits.len() as u64,
                relation: "eq".to_string(),
            },
            max_score: Some(1.0),
            hits,
        },
    };

    Ok(Json(response))
}

/// 处理 POST /datasource/_search 请求（兼容版本）
pub async fn search_datasource_post(
    State(config_manager): State<Arc<ConfigManager>>,
    headers: HeaderMap,
    body: Bytes,
) -> Result<Json<SearchResponse>, StatusCode> {
    let content_type = headers
        .get("content-type")
        .and_then(|v| v.to_str().ok())
        .unwrap_or("application/json");

    info!("处理POST搜索数据源请求: content-type={}", content_type);

    // 解析请求体，支持多种格式
    let query = if content_type.contains("application/json") {
        // JSON 格式
        match serde_json::from_slice::<Value>(&body) {
            Ok(value) => {
                // 尝试解析为SearchQuery，如果失败则使用默认查询
                if let Ok(search_query) = serde_json::from_value::<SearchQuery>(value.clone()) {
                    search_query
                } else {
                    // 如果不是标准的SearchQuery格式，提取查询字符串
                    let q = value
                        .get("q")
                        .or_else(|| value.get("query"))
                        .and_then(|v| v.as_str())
                        .map(|s| s.to_string());

                    SearchQuery {
                        q,
                        size: value.get("size").and_then(|v| v.as_u64()),
                        from: value.get("from").and_then(|v| v.as_u64()),
                        sort: value
                            .get("sort")
                            .and_then(|v| v.as_str())
                            .map(|s| s.to_string()),
                        extra: HashMap::new(),
                    }
                }
            }
            Err(e) => {
                warn!("解析JSON请求体失败: {}", e);
                SearchQuery {
                    q: None,
                    size: None,
                    from: None,
                    sort: None,
                    extra: HashMap::new(),
                }
            }
        }
    } else if content_type.contains("application/x-www-form-urlencoded") {
        // URL 编码格式
        let body_str = String::from_utf8_lossy(&body);
        info!("接收到表单数据: {}", body_str);

        // 解析表单数据
        let mut query = SearchQuery {
            q: None,
            size: None,
            from: None,
            sort: None,
            extra: HashMap::new(),
        };

        for pair in body_str.split('&') {
            if let Some((key, value)) = pair.split_once('=') {
                let key = urlencoding::decode(key).unwrap_or_default();
                let value = urlencoding::decode(value).unwrap_or_default();

                match key.as_ref() {
                    "q" => query.q = Some(value.to_string()),
                    "size" => query.size = value.parse().ok(),
                    "from" => query.from = value.parse().ok(),
                    "sort" => query.sort = Some(value.to_string()),
                    _ => {
                        query
                            .extra
                            .insert(key.to_string(), json!(value.to_string()));
                    }
                }
            }
        }

        query
    } else {
        // 其他格式，使用默认查询
        info!("未知内容类型，使用默认查询");
        SearchQuery {
            q: None,
            size: None,
            from: None,
            sort: None,
            extra: HashMap::new(),
        }
    };

    info!("解析的查询参数: {:?}", query);

    // 临时返回模拟数据
    let mock_datasource = DataSource {
        id: Some("ds_001".to_string()),
        r#type: "connector".to_string(),
        name: "Sample DataSource 1".to_string(),
        description: Some("A sample data source for testing".to_string()),
        icon: None,
        category: Some("database".to_string()),
        tags: None,
        connector: Some(crate::models::connector::ConnectorConfig {
            id: "hugo_site".to_string(),
            config: Some(json!({"host": "localhost", "port": 5432})),
        }),
        sync_enabled: true,
        enabled: true,
        created: Some(chrono::Utc::now()),
        updated: Some(chrono::Utc::now()),
    };

    let hits = vec![SearchHit {
        _index: "datasource".to_string(),
        _type: "_doc".to_string(),
        _id: mock_datasource.id.clone().unwrap_or_default(),
        _score: Some(1.0),
        _source: mock_datasource,
    }];

    let response = SearchResponse {
        took: 3,
        timed_out: false,
        hits: SearchHits {
            total: SearchTotal {
                value: hits.len() as u64,
                relation: "eq".to_string(),
            },
            max_score: Some(1.0),
            hits,
        },
    };

    Ok(Json(response))
}

/// 处理 OPTIONS /datasource/_search 请求（CORS 预检）
pub async fn search_datasource_options() -> Result<StatusCode, StatusCode> {
    info!("处理OPTIONS搜索数据源请求");
    Ok(StatusCode::OK)
}

/// 创建文档处理器
///
/// 处理 POST /datasource/:id/_doc 请求
pub async fn create_document_handler(
    State(app_state): State<AppState>,
    Path(datasource_id): Path<String>,
    Json(request): Json<CreateDocumentRequest>,
) -> Result<Json<CreateDocumentResponse>, StatusCode> {
    info!("处理创建文档请求: datasource_id={}", datasource_id);

    // 获取数据源服务
    let datasource_service = match &app_state.datasource_service {
        Some(service) => service,
        None => {
            error!("数据源服务未初始化");
            return Err(StatusCode::INTERNAL_SERVER_ERROR);
        }
    };

    // 调用业务服务创建文档
    match datasource_service
        .create_document(&datasource_id, request, None)
        .await
    {
        Ok(id) => {
            let response = CreateDocumentResponse {
                id,
                result: "created".to_string(),
            };
            Ok(Json(response))
        }
        Err(e) => {
            error!("创建文档失败: {}", e);
            Err(StatusCode::BAD_REQUEST)
        }
    }
}

/// 创建指定ID文档处理器
///
/// 处理 POST /datasource/:id/_doc/:doc_id 请求
pub async fn create_document_with_id_handler(
    State(app_state): State<AppState>,
    Path((datasource_id, doc_id)): Path<(String, String)>,
    Json(request): Json<CreateDocumentRequest>,
) -> Result<Json<CreateDocumentResponse>, StatusCode> {
    info!(
        "处理创建指定ID文档请求: datasource_id={}, doc_id={}",
        datasource_id, doc_id
    );

    // 获取数据源服务
    let datasource_service = match &app_state.datasource_service {
        Some(service) => service,
        None => {
            error!("数据源服务未初始化");
            return Err(StatusCode::INTERNAL_SERVER_ERROR);
        }
    };

    // 调用业务服务创建文档
    match datasource_service
        .create_document(&datasource_id, request, Some(doc_id.clone()))
        .await
    {
        Ok(_) => {
            let response = CreateDocumentResponse {
                id: doc_id,
                result: "created".to_string(),
            };
            Ok(Json(response))
        }
        Err(e) => {
            error!("创建指定ID文档失败: {}", e);
            Err(StatusCode::BAD_REQUEST)
        }
    }
}
