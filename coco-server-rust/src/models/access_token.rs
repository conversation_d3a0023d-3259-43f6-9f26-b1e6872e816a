use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// API访问令牌模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccessToken {
    /// 令牌ID
    pub id: String,
    /// 访问令牌字符串
    pub access_token: String,
    /// 用户ID
    pub user_id: String,
    /// 令牌名称
    pub name: String,
    /// 提供商类型
    pub provider: String,
    /// 令牌类型
    pub token_type: String,
    /// 用户角色
    pub roles: Vec<String>,
    /// 权限列表
    pub permissions: Vec<String>,
    /// 过期时间戳
    pub expire_in: i64,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 最后使用时间
    pub last_used: Option<DateTime<Utc>>,
    /// 是否激活
    pub is_active: bool,
}

impl AccessToken {
    /// 创建新的API令牌
    pub fn new(user_id: String, name: String) -> Self {
        let token_value = format!("{}-{}", Uuid::new_v4(), generate_random_string(64));
        let expire_in = Utc::now().timestamp() + 365 * 24 * 3600; // 365天

        Self {
            id: Uuid::new_v4().to_string(),
            access_token: token_value,
            user_id,
            name,
            provider: "access_token".to_string(),
            token_type: "api".to_string(),
            roles: vec!["admin".to_string()], // 默认管理员角色
            permissions: vec![],
            expire_in,
            created_at: Utc::now(),
            last_used: None,
            is_active: true,
        }
    }

    /// 检查令牌是否过期
    pub fn is_expired(&self) -> bool {
        Utc::now().timestamp() > self.expire_in
    }

    /// 更新最后使用时间
    pub fn update_last_used(&mut self) {
        self.last_used = Some(Utc::now());
    }

    /// 撤销令牌
    pub fn revoke(&mut self) {
        self.is_active = false;
    }
}

/// 生成随机字符串
fn generate_random_string(length: usize) -> String {
    use rand::Rng;
    const CHARSET: &[u8] = b"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let mut rng = rand::rng();

    (0..length)
        .map(|_| {
            let idx = rng.random_range(0..CHARSET.len());
            CHARSET[idx] as char
        })
        .collect()
}

/// API令牌请求模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateTokenRequest {
    /// 令牌名称（可选）
    pub name: Option<String>,
}

/// API令牌响应模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateTokenResponse {
    /// 访问令牌
    pub access_token: String,
    /// 过期时间（秒）
    pub expire_in: i64,
}

/// 令牌列表项模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenListItem {
    /// 令牌ID
    pub id: String,
    /// 令牌名称
    pub name: String,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 最后使用时间
    pub last_used: Option<DateTime<Utc>>,
    /// 是否激活
    pub is_active: bool,
    /// 过期时间戳
    pub expire_in: i64,
}

impl From<AccessToken> for TokenListItem {
    fn from(token: AccessToken) -> Self {
        Self {
            id: token.id,
            name: token.name,
            created_at: token.created_at,
            last_used: token.last_used,
            is_active: token.is_active,
            expire_in: token.expire_in,
        }
    }
}

/// 令牌重命名请求模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RenameTokenRequest {
    /// 新名称
    pub name: String,
}

/// 生成API令牌名称
pub fn generate_api_token_name(prefix: Option<&str>) -> String {
    let prefix = prefix.unwrap_or("token");
    let timestamp = Utc::now().timestamp_millis();
    let random_str = generate_random_string(8);
    format!("{}_{}_{}", prefix, timestamp, random_str)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_access_token_creation() {
        let user_id = "test-user".to_string();
        let name = "test-token".to_string();
        let token = AccessToken::new(user_id.clone(), name.clone());

        assert_eq!(token.user_id, user_id);
        assert_eq!(token.name, name);
        assert_eq!(token.provider, "access_token");
        assert_eq!(token.token_type, "api");
        assert_eq!(token.roles, vec!["admin".to_string()]);
        assert!(token.is_active);
        assert!(token.last_used.is_none());

        // 验证令牌格式：UUID-64位随机字符串
        let parts: Vec<&str> = token.access_token.split('-').collect();
        assert!(parts.len() >= 5); // UUID有4个连字符，加上我们的分隔符

        // 验证过期时间设置正确（365天）
        let expected_expire = Utc::now().timestamp() + 365 * 24 * 3600;
        assert!((token.expire_in - expected_expire).abs() < 10); // 允许10秒误差
    }

    #[test]
    fn test_access_token_expiration() {
        let user_id = "test-user".to_string();
        let name = "test-token".to_string();
        let mut token = AccessToken::new(user_id, name);

        // 新创建的令牌不应该过期
        assert!(!token.is_expired());

        // 设置过期时间为过去
        token.expire_in = Utc::now().timestamp() - 3600; // 1小时前
        assert!(token.is_expired());
    }

    #[test]
    fn test_access_token_update_last_used() {
        let user_id = "test-user".to_string();
        let name = "test-token".to_string();
        let mut token = AccessToken::new(user_id, name);

        assert!(token.last_used.is_none());

        token.update_last_used();
        assert!(token.last_used.is_some());

        let last_used = token.last_used.unwrap();
        let now = Utc::now();
        assert!((now - last_used).num_seconds() < 5); // 允许5秒误差
    }

    #[test]
    fn test_access_token_revoke() {
        let user_id = "test-user".to_string();
        let name = "test-token".to_string();
        let mut token = AccessToken::new(user_id, name);

        assert!(token.is_active);

        token.revoke();
        assert!(!token.is_active);
    }

    #[test]
    fn test_generate_random_string() {
        let length = 64;
        let random_str = generate_random_string(length);

        assert_eq!(random_str.len(), length);

        // 验证只包含允许的字符
        for ch in random_str.chars() {
            assert!(ch.is_ascii_alphanumeric());
        }

        // 生成两个随机字符串应该不同
        let random_str2 = generate_random_string(length);
        assert_ne!(random_str, random_str2);
    }

    #[test]
    fn test_create_token_request_serialization() {
        let request = CreateTokenRequest {
            name: Some("test-token".to_string()),
        };

        let json = serde_json::to_string(&request).unwrap();
        assert!(json.contains("test-token"));

        let deserialized: CreateTokenRequest = serde_json::from_str(&json).unwrap();
        assert_eq!(deserialized.name, Some("test-token".to_string()));

        // 测试None情况
        let request_none = CreateTokenRequest { name: None };
        let json_none = serde_json::to_string(&request_none).unwrap();
        let deserialized_none: CreateTokenRequest = serde_json::from_str(&json_none).unwrap();
        assert_eq!(deserialized_none.name, None);
    }

    #[test]
    fn test_create_token_response_serialization() {
        let response = CreateTokenResponse {
            access_token: "test-token-123".to_string(),
            expire_in: 31536000,
        };

        let json = serde_json::to_string(&response).unwrap();
        assert!(json.contains("test-token-123"));
        assert!(json.contains("31536000"));

        let deserialized: CreateTokenResponse = serde_json::from_str(&json).unwrap();
        assert_eq!(deserialized.access_token, "test-token-123");
        assert_eq!(deserialized.expire_in, 31536000);
    }

    #[test]
    fn test_token_list_item_from_access_token() {
        let user_id = "test-user".to_string();
        let name = "test-token".to_string();
        let token = AccessToken::new(user_id, name.clone());

        let list_item: TokenListItem = token.clone().into();

        assert_eq!(list_item.id, token.id);
        assert_eq!(list_item.name, token.name);
        assert_eq!(list_item.created_at, token.created_at);
        assert_eq!(list_item.last_used, token.last_used);
        assert_eq!(list_item.is_active, token.is_active);
        assert_eq!(list_item.expire_in, token.expire_in);
    }

    #[test]
    fn test_rename_token_request_serialization() {
        let request = RenameTokenRequest {
            name: "new-name".to_string(),
        };

        let json = serde_json::to_string(&request).unwrap();
        assert!(json.contains("new-name"));

        let deserialized: RenameTokenRequest = serde_json::from_str(&json).unwrap();
        assert_eq!(deserialized.name, "new-name");
    }

    #[test]
    fn test_generate_api_token_name() {
        // 测试默认前缀
        let name1 = generate_api_token_name(None);
        assert!(name1.starts_with("token_"));

        // 测试自定义前缀
        let name2 = generate_api_token_name(Some("custom"));
        assert!(name2.starts_with("custom_"));

        // 验证名称包含时间戳和随机字符串
        let parts: Vec<&str> = name1.split('_').collect();
        assert_eq!(parts.len(), 3);
        assert_eq!(parts[0], "token");

        // 时间戳应该是数字
        assert!(parts[1].parse::<i64>().is_ok());

        // 随机字符串应该是8个字符
        assert_eq!(parts[2].len(), 8);

        // 两次生成的名称应该不同
        let name3 = generate_api_token_name(None);
        assert_ne!(name1, name3);
    }
}
