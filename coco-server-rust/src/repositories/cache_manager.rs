use chrono::{DateTime, Duration, Utc};
use dashmap::DashMap;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::time::{interval, Duration as TokioDuration};
use tracing::{debug, info};

/// 缓存项结构体
#[derive(Debug, Clone)]
struct CacheItem<T> {
    /// 缓存的数据
    data: T,
    /// 过期时间
    expires_at: DateTime<Utc>,
}

/// 缓存管理器
/// 
/// 提供线程安全的内存缓存功能，支持TTL过期策略
#[derive(Clone)]
pub struct CacheManager {
    /// 缓存存储，使用DashMap提供线程安全的HashMap
    cache: Arc<DashMap<String, CacheItem<String>>>,
    /// 默认TTL（秒）
    default_ttl: i64,
}

/// 缓存统计信息
#[derive(Debug, Serialize, Deserialize)]
pub struct CacheStats {
    /// 缓存项总数
    pub total_items: usize,
    /// 命中次数
    pub hits: u64,
    /// 未命中次数
    pub misses: u64,
    /// 命中率
    pub hit_rate: f64,
}

impl CacheManager {
    /// 创建新的缓存管理器
    /// 
    /// # 参数
    /// * `default_ttl` - 默认TTL时间（秒）
    pub fn new(default_ttl: i64) -> Self {
        let cache_manager = Self {
            cache: Arc::new(DashMap::new()),
            default_ttl,
        };
        
        // 启动清理任务
        cache_manager.start_cleanup_task();
        
        info!("缓存管理器初始化完成，默认TTL: {}秒", default_ttl);
        cache_manager
    }
    
    /// 设置缓存项
    /// 
    /// # 参数
    /// * `key` - 缓存键
    /// * `value` - 缓存值
    /// * `ttl` - TTL时间（秒），如果为None则使用默认TTL
    pub fn set(&self, key: String, value: String, ttl: Option<i64>) -> Result<(), String> {
        let ttl_seconds = ttl.unwrap_or(self.default_ttl);
        let expires_at = Utc::now() + Duration::seconds(ttl_seconds);
        
        let cache_item = CacheItem {
            data: value,
            expires_at,
        };
        
        self.cache.insert(key.clone(), cache_item);
        debug!("缓存项已设置: key={}, ttl={}秒", key, ttl_seconds);
        
        Ok(())
    }
    
    /// 获取缓存项
    /// 
    /// # 参数
    /// * `key` - 缓存键
    /// 
    /// # 返回
    /// 如果缓存项存在且未过期，返回Some(value)，否则返回None
    pub fn get(&self, key: &str) -> Option<String> {
        if let Some(item) = self.cache.get(key) {
            if item.expires_at > Utc::now() {
                debug!("缓存命中: key={}", key);
                return Some(item.data.clone());
            } else {
                // 缓存已过期，删除它
                debug!("缓存项已过期，正在删除: key={}", key);
                drop(item); // 释放读锁
                self.cache.remove(key);
            }
        }
        
        debug!("缓存未命中: key={}", key);
        None
    }
    
    /// 删除缓存项
    /// 
    /// # 参数
    /// * `key` - 缓存键
    pub fn remove(&self, key: &str) -> bool {
        let removed = self.cache.remove(key).is_some();
        if removed {
            debug!("缓存项已删除: key={}", key);
        }
        removed
    }
    
    /// 清空所有缓存
    pub fn clear(&self) {
        let count = self.cache.len();
        self.cache.clear();
        info!("已清空所有缓存，共删除{}个项目", count);
    }
    
    /// 检查缓存项是否存在且未过期
    /// 
    /// # 参数
    /// * `key` - 缓存键
    pub fn exists(&self, key: &str) -> bool {
        if let Some(item) = self.cache.get(key) {
            if item.expires_at > Utc::now() {
                return true;
            } else {
                // 缓存已过期，删除它
                drop(item); // 释放读锁
                self.cache.remove(key);
            }
        }
        false
    }
    
    /// 获取缓存统计信息
    pub fn stats(&self) -> CacheStats {
        CacheStats {
            total_items: self.cache.len(),
            hits: 0,    // TODO: 实现命中统计
            misses: 0,  // TODO: 实现未命中统计
            hit_rate: 0.0, // TODO: 计算命中率
        }
    }
    
    /// 清理过期的缓存项
    pub fn cleanup_expired(&self) {
        let now = Utc::now();
        let mut expired_keys = Vec::new();
        
        // 收集过期的键
        for entry in self.cache.iter() {
            if entry.value().expires_at <= now {
                expired_keys.push(entry.key().clone());
            }
        }
        
        // 删除过期的项
        let mut removed_count = 0;
        for key in expired_keys {
            if self.cache.remove(&key).is_some() {
                removed_count += 1;
            }
        }
        
        if removed_count > 0 {
            debug!("清理了{}个过期的缓存项", removed_count);
        }
    }
    
    /// 启动后台清理任务
    fn start_cleanup_task(&self) {
        let cache = self.cache.clone();
        
        tokio::spawn(async move {
            let mut interval = interval(TokioDuration::from_secs(60)); // 每分钟清理一次
            
            loop {
                interval.tick().await;
                
                let now = Utc::now();
                let mut expired_keys = Vec::new();
                
                // 收集过期的键
                for entry in cache.iter() {
                    if entry.value().expires_at <= now {
                        expired_keys.push(entry.key().clone());
                    }
                }
                
                // 删除过期的项
                let mut removed_count = 0;
                for key in expired_keys {
                    if cache.remove(&key).is_some() {
                        removed_count += 1;
                    }
                }
                
                if removed_count > 0 {
                    debug!("后台清理任务删除了{}个过期的缓存项", removed_count);
                }
            }
        });
    }
}

/// 缓存键常量定义
pub mod cache_keys {
    /// 数据源主缓存键前缀
    pub const DATASOURCE_PRIMARY_PREFIX: &str = "datasource_primary";
    
    /// 禁用的数据源ID列表缓存键
    pub const DISABLED_DATASOURCE_IDS: &str = "disabled_datasource_ids";
    
    /// 启用的数据源ID列表缓存键
    pub const ENABLED_DATASOURCE_IDS: &str = "enabled_datasource_ids";
    
    /// 数据源项目缓存键前缀
    pub const DATASOURCE_ITEMS_PREFIX: &str = "datasource_items";
    
    /// 构建缓存键
    /// 
    /// # 参数
    /// * `prefix` - 键前缀
    /// * `key` - 具体键值
    pub fn build_key(prefix: &str, key: &str) -> String {
        format!("{}:{}", prefix, key)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::time::{sleep, Duration};
    
    #[tokio::test]
    async fn test_cache_basic_operations() {
        let cache = CacheManager::new(60); // 60秒TTL
        
        // 测试设置和获取
        cache.set("test_key".to_string(), "test_value".to_string(), None).unwrap();
        assert_eq!(cache.get("test_key"), Some("test_value".to_string()));
        
        // 测试不存在的键
        assert_eq!(cache.get("nonexistent_key"), None);
        
        // 测试删除
        assert!(cache.remove("test_key"));
        assert_eq!(cache.get("test_key"), None);
        assert!(!cache.remove("test_key")); // 再次删除应该返回false
    }
    
    #[tokio::test]
    async fn test_cache_expiration() {
        let cache = CacheManager::new(60);
        
        // 设置一个1秒过期的缓存项
        cache.set("expire_test".to_string(), "value".to_string(), Some(1)).unwrap();
        assert_eq!(cache.get("expire_test"), Some("value".to_string()));
        
        // 等待过期
        sleep(Duration::from_secs(2)).await;
        assert_eq!(cache.get("expire_test"), None);
    }
    
    #[test]
    fn test_cache_keys() {
        let key = cache_keys::build_key(cache_keys::DATASOURCE_PRIMARY_PREFIX, "123");
        assert_eq!(key, "datasource_primary:123");
    }
}
