/// 数据访问层模块
///
/// 此模块包含所有数据访问相关的组件：
/// - surrealdb_client: SurrealDB客户端封装（替代elasticsearch）
/// - cache_manager: 缓存管理器
/// - datasource_repo: 数据源仓库
/// - model_provider_repo: 模型提供商仓库
/// - token_repository: 令牌数据访问层
/// - query_builder: SurrealDB查询构建器
/// - search_params: 搜索参数结构
// 暂时注释掉elasticsearch，使用SurrealDB替代
// pub mod elasticsearch;
pub mod cache_manager;
pub mod datasource_repo;
pub mod model_provider_repo;
pub mod query_builder;
pub mod search_params;
pub mod token_repository;

// 重新导出主要类型
// pub use elasticsearch::*;
pub use query_builder::*;
pub use search_params::*;
