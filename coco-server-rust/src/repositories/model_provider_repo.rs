use std::{collections::HashMap, sync::Arc, time::Instant};

use anyhow::Result;
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use surrealdb::{engine::remote::ws::Client, sql::Thing, Surreal};
use tracing::{error, info};

use super::{QueryBuilder, SearchParams};
use crate::{error::error::CocoError, models::model_provider::*};

/// 搜索查询结构
#[derive(Debug, Deserialize)]
pub struct SearchQuery {
    /// 查询字符串
    pub q: Option<String>,
    /// 分页大小
    pub size: Option<usize>,
    /// 分页偏移
    pub from: Option<usize>,
    /// 排序字段
    pub sort: Option<String>,
    /// 过滤条件
    pub filters: Option<HashMap<String, serde_json::Value>>,
}

/// 搜索响应结构，兼容Elasticsearch格式
#[derive(Debug, Serialize)]
pub struct SearchResponse {
    pub took: u64,
    pub timed_out: bool,
    pub hits: SearchHits,
}

#[derive(Debug, Serialize)]
pub struct SearchHits {
    pub total: SearchTotal,
    pub max_score: Option<f64>,
    pub hits: Vec<SearchHit>,
}

#[derive(Debug, Serialize)]
pub struct SearchTotal {
    pub value: usize,
    pub relation: String,
}

#[derive(Debug, Serialize)]
pub struct SearchHit {
    #[serde(rename = "_index")]
    pub index: String,
    #[serde(rename = "_type")]
    pub doc_type: String,
    #[serde(rename = "_id")]
    pub id: String,
    #[serde(rename = "_score")]
    pub score: Option<f64>,
    #[serde(rename = "_source")]
    pub source: ModelProvider,
}

/// ModelProvider仓储接口
/// 定义了所有数据访问操作的抽象
#[async_trait]
pub trait ModelProviderRepository: Send + Sync {
    /// 创建新的模型提供商
    ///
    /// # 参数
    /// * `provider` - 要创建的模型提供商
    ///
    /// # 返回
    /// * `Ok(String)` - 创建成功，返回生成的ID
    /// * `Err(CocoError)` - 创建失败
    async fn create(&self, provider: &ModelProvider) -> Result<String, CocoError>;

    /// 根据ID获取模型提供商
    ///
    /// # 参数
    /// * `id` - 模型提供商ID
    ///
    /// # 返回
    /// * `Ok(Some(ModelProvider))` - 找到模型提供商
    /// * `Ok(None)` - 未找到模型提供商
    /// * `Err(CocoError)` - 查询失败
    async fn get_by_id(&self, id: &str) -> Result<Option<ModelProvider>, CocoError>;

    /// 更新模型提供商
    ///
    /// # 参数
    /// * `provider` - 要更新的模型提供商（包含完整数据）
    ///
    /// # 返回
    /// * `Ok(())` - 更新成功
    /// * `Err(CocoError)` - 更新失败
    async fn update(&self, provider: &ModelProvider) -> Result<(), CocoError>;

    /// 删除模型提供商
    ///
    /// # 参数
    /// * `id` - 要删除的模型提供商ID
    ///
    /// # 返回
    /// * `Ok(())` - 删除成功
    /// * `Err(CocoError)` - 删除失败
    async fn delete(&self, id: &str) -> Result<(), CocoError>;

    /// 搜索模型提供商
    ///
    /// # 参数
    /// * `query` - 搜索查询参数
    ///
    /// # 返回
    /// * `Ok(SearchResponse)` - 搜索成功，返回结果
    /// * `Err(CocoError)` - 搜索失败
    async fn search(&self, query: &SearchQuery) -> Result<SearchResponse, CocoError>;

    /// 使用新的搜索参数搜索模型提供商
    ///
    /// # 参数
    /// * `params` - 标准化的搜索参数
    ///
    /// # 返回
    /// * `Ok(SearchResponse)` - 搜索成功，返回结果
    /// * `Err(CocoError)` - 搜索失败
    async fn search_with_params(&self, params: &SearchParams) -> Result<SearchResponse, CocoError>;

    /// 检查模型提供商是否存在
    ///
    /// # 参数
    /// * `id` - 模型提供商ID
    ///
    /// # 返回
    /// * `Ok(true)` - 存在
    /// * `Ok(false)` - 不存在
    /// * `Err(CocoError)` - 检查失败
    async fn exists(&self, id: &str) -> Result<bool, CocoError>;

    /// 根据名称查找模型提供商（用于唯一性检查）
    ///
    /// # 参数
    /// * `name` - 模型提供商名称
    ///
    /// # 返回
    /// * `Ok(Some(ModelProvider))` - 找到同名的模型提供商
    /// * `Ok(None)` - 未找到同名的模型提供商
    /// * `Err(CocoError)` - 查询失败
    async fn find_by_name(&self, name: &str) -> Result<Option<ModelProvider>, CocoError>;

    /// 获取所有启用的模型提供商
    ///
    /// # 返回
    /// * `Ok(Vec<ModelProvider>)` - 启用的模型提供商列表
    /// * `Err(CocoError)` - 查询失败
    async fn get_enabled(&self) -> Result<Vec<ModelProvider>, CocoError>;

    /// 获取所有内置的模型提供商
    ///
    /// # 返回
    /// * `Ok(Vec<ModelProvider>)` - 内置的模型提供商列表
    /// * `Err(CocoError)` - 查询失败
    async fn get_builtin(&self) -> Result<Vec<ModelProvider>, CocoError>;
}

impl SearchQuery {
    /// 创建新的搜索查询
    pub fn new() -> Self {
        Self {
            q: None,
            size: Some(20), // 默认分页大小
            from: Some(0),  // 默认偏移
            sort: None,
            filters: None,
        }
    }

    /// 设置查询字符串
    pub fn with_query(mut self, q: String) -> Self {
        self.q = Some(q);
        self
    }

    /// 设置分页参数
    pub fn with_pagination(mut self, size: usize, from: usize) -> Self {
        self.size = Some(size);
        self.from = Some(from);
        self
    }

    /// 设置排序
    pub fn with_sort(mut self, sort: String) -> Self {
        self.sort = Some(sort);
        self
    }

    /// 添加过滤条件
    pub fn with_filter(mut self, key: String, value: serde_json::Value) -> Self {
        if self.filters.is_none() {
            self.filters = Some(HashMap::new());
        }
        self.filters.as_mut().unwrap().insert(key, value);
        self
    }

    /// 获取分页大小，如果未设置则返回默认值
    pub fn get_size(&self) -> usize {
        self.size.unwrap_or(20)
    }

    /// 获取分页偏移，如果未设置则返回默认值
    pub fn get_from(&self) -> usize {
        self.from.unwrap_or(0)
    }
}

impl Default for SearchQuery {
    fn default() -> Self {
        Self::new()
    }
}

impl SearchResponse {
    /// 创建新的搜索响应
    pub fn new(hits: Vec<ModelProvider>, total: usize, took: u64) -> Self {
        let search_hits: Vec<SearchHit> = hits
            .into_iter()
            .enumerate()
            .map(|(i, provider)| SearchHit {
                index: "model_provider".to_string(),
                doc_type: "_doc".to_string(),
                id: provider.id.clone(),
                score: Some(1.0), // 默认评分
                source: provider,
            })
            .collect();

        Self {
            took,
            timed_out: false,
            hits: SearchHits {
                total: SearchTotal {
                    value: total,
                    relation: "eq".to_string(),
                },
                max_score: Some(1.0),
                hits: search_hits,
            },
        }
    }

    /// 创建空的搜索响应
    pub fn empty(took: u64) -> Self {
        Self {
            took,
            timed_out: false,
            hits: SearchHits {
                total: SearchTotal {
                    value: 0,
                    relation: "eq".to_string(),
                },
                max_score: None,
                hits: vec![],
            },
        }
    }
}

/// SurrealDB实现的ModelProvider仓储
pub struct SurrealModelProviderRepository {
    db: Arc<Surreal<Client>>,
    query_builder: QueryBuilder,
}

impl SurrealModelProviderRepository {
    /// 创建新的SurrealDB仓储实例
    pub fn new(db: Arc<Surreal<Client>>) -> Self {
        Self {
            db,
            query_builder: QueryBuilder::new(),
        }
    }

    /// 构建SurrealDB的Thing ID
    fn build_thing_id(&self, id: &str) -> Thing {
        Thing::from(("model_provider", id))
    }
}

#[async_trait]
impl ModelProviderRepository for SurrealModelProviderRepository {
    async fn create(&self, provider: &ModelProvider) -> Result<String, CocoError> {
        info!("创建模型提供商: {}", provider.name);

        let start = Instant::now();

        // 检查名称是否已存在
        if let Some(_existing) = self.find_by_name(&provider.name).await? {
            return Err(CocoError::model_provider_validation(&format!(
                "模型提供商名称 '{}' 已存在",
                provider.name
            )));
        }

        // 创建记录
        let result: Option<ModelProvider> = self
            .db
            .create("model_provider")
            .content(provider.clone())
            .await
            .map_err(|e| {
                error!("创建模型提供商失败: {}", e);
                CocoError::Database(format!("创建模型提供商失败: {}", e))
            })?;

        let created_provider = result
            .ok_or_else(|| CocoError::Database("创建模型提供商失败：未返回结果".to_string()))?;

        let created_id = created_provider.id.clone();
        info!(
            "模型提供商创建成功: {} (耗时: {:?})",
            created_id,
            start.elapsed()
        );

        Ok(created_id)
    }

    async fn get_by_id(&self, id: &str) -> Result<Option<ModelProvider>, CocoError> {
        let start = Instant::now();

        let record_id = format!("model_provider:{}", id);
        let result: Vec<ModelProvider> = self.db.select(record_id).await.map_err(|e| {
            error!("查询模型提供商失败: {}", e);
            CocoError::Database(format!("查询模型提供商失败: {}", e))
        })?;

        let result = result.into_iter().next();

        if result.is_some() {
            info!("查询模型提供商成功: {} (耗时: {:?})", id, start.elapsed());
        }

        Ok(result)
    }

    async fn update(&self, provider: &ModelProvider) -> Result<(), CocoError> {
        info!("更新模型提供商: {}", provider.id);

        let start = Instant::now();

        // 检查是否存在
        if !self.exists(&provider.id).await? {
            return Err(CocoError::model_provider_not_found(&provider.id));
        }

        // 如果名称发生变化，检查新名称是否已被其他提供商使用
        if let Some(existing) = self.find_by_name(&provider.name).await? {
            if existing.id != provider.id {
                return Err(CocoError::model_provider_validation(&format!(
                    "模型提供商名称 '{}' 已被其他提供商使用",
                    provider.name
                )));
            }
        }

        let record_id = format!("model_provider:{}", provider.id);
        let _result: Vec<ModelProvider> = self
            .db
            .update(record_id)
            .content(provider.clone())
            .await
            .map_err(|e| {
                error!("更新模型提供商失败: {}", e);
                CocoError::Database(format!("更新模型提供商失败: {}", e))
            })?;

        info!(
            "模型提供商更新成功: {} (耗时: {:?})",
            provider.id,
            start.elapsed()
        );
        Ok(())
    }

    async fn delete(&self, id: &str) -> Result<(), CocoError> {
        info!("删除模型提供商: {}", id);

        let start = Instant::now();

        // 检查是否存在
        let provider = self
            .get_by_id(id)
            .await?
            .ok_or_else(|| CocoError::model_provider_not_found(id))?;

        // 检查是否为内置提供商
        if provider.builtin {
            return Err(CocoError::builtin_provider_protection(
                "内置模型提供商不能被删除",
            ));
        }

        let record_id = format!("model_provider:{}", id);
        let _result: Vec<ModelProvider> = self.db.delete(record_id).await.map_err(|e| {
            error!("删除模型提供商失败: {}", e);
            CocoError::Database(format!("删除模型提供商失败: {}", e))
        })?;

        info!("模型提供商删除成功: {} (耗时: {:?})", id, start.elapsed());
        Ok(())
    }

    async fn search(&self, query: &SearchQuery) -> Result<SearchResponse, CocoError> {
        // 将SearchQuery转换为SearchParams
        let mut params = SearchParams::new().with_pagination(query.get_size(), query.get_from());

        if let Some(q) = &query.q {
            params = params.with_query(q.clone());
        }

        if let Some(sort) = &query.sort {
            params = params.with_sort(sort.clone());
        }

        if let Some(filters) = &query.filters {
            for (key, value) in filters {
                params = params.with_filter(key.clone(), value.clone());
            }
        }

        // 使用新的search_with_params方法
        self.search_with_params(&params).await
    }

    async fn search_with_params(&self, params: &SearchParams) -> Result<SearchResponse, CocoError> {
        let start = Instant::now();

        // 验证搜索参数
        if let Err(validation_error) = params.validate() {
            return Err(CocoError::model_provider_validation(&validation_error));
        }

        // 使用QueryBuilder构建查询
        let query_result = self.query_builder.build_search_query(params);
        info!("执行搜索查询: {}", query_result.sql);

        // 执行查询
        let mut response = self.db.query(&query_result.sql).await.map_err(|e| {
            error!("搜索模型提供商失败: {}", e);
            CocoError::Database(format!("搜索模型提供商失败: {}", e))
        })?;

        let providers: Vec<ModelProvider> = response.take(0).map_err(|e| {
            error!("解析搜索结果失败: {}", e);
            CocoError::Database(format!("解析搜索结果失败: {}", e))
        })?;

        // 获取总数
        let count_result = self.query_builder.build_count_query(params);
        let mut count_response = self.db.query(&count_result.sql).await.map_err(|e| {
            error!("获取搜索总数失败: {}", e);
            CocoError::Database(format!("获取搜索总数失败: {}", e))
        })?;

        let total: Option<i64> = count_response.take(0).map_err(|e| {
            error!("解析搜索总数失败: {}", e);
            CocoError::Database(format!("解析搜索总数失败: {}", e))
        })?;

        let total_count = total.unwrap_or(0) as usize;
        let took = start.elapsed().as_millis() as u64;

        info!(
            "搜索完成: 找到 {} 条记录 (耗时: {}ms)",
            providers.len(),
            took
        );

        Ok(SearchResponse::new(providers, total_count, took))
    }

    async fn exists(&self, id: &str) -> Result<bool, CocoError> {
        let result = self.get_by_id(id).await?;
        Ok(result.is_some())
    }

    async fn find_by_name(&self, name: &str) -> Result<Option<ModelProvider>, CocoError> {
        let sql = "SELECT * FROM model_provider WHERE name = $name LIMIT 1";

        let mut response = self
            .db
            .query(sql)
            .bind(("name", name.to_string()))
            .await
            .map_err(|e| {
                error!("按名称查询模型提供商失败: {}", e);
                CocoError::Database(format!("按名称查询模型提供商失败: {}", e))
            })?;

        let providers: Vec<ModelProvider> = response.take(0).map_err(|e| {
            error!("解析查询结果失败: {}", e);
            CocoError::Database(format!("解析查询结果失败: {}", e))
        })?;

        Ok(providers.into_iter().next())
    }

    async fn get_enabled(&self) -> Result<Vec<ModelProvider>, CocoError> {
        let sql = "SELECT * FROM model_provider WHERE enabled = true ORDER BY name ASC";

        let mut response = self.db.query(sql).await.map_err(|e| {
            error!("查询启用的模型提供商失败: {}", e);
            CocoError::Database(format!("查询启用的模型提供商失败: {}", e))
        })?;

        let providers: Vec<ModelProvider> = response.take(0).map_err(|e| {
            error!("解析查询结果失败: {}", e);
            CocoError::Database(format!("解析查询结果失败: {}", e))
        })?;

        Ok(providers)
    }

    async fn get_builtin(&self) -> Result<Vec<ModelProvider>, CocoError> {
        let sql = "SELECT * FROM model_provider WHERE builtin = true ORDER BY name ASC";

        let mut response = self.db.query(sql).await.map_err(|e| {
            error!("查询内置模型提供商失败: {}", e);
            CocoError::Database(format!("查询内置模型提供商失败: {}", e))
        })?;

        let providers: Vec<ModelProvider> = response.take(0).map_err(|e| {
            error!("解析查询结果失败: {}", e);
            CocoError::Database(format!("解析查询结果失败: {}", e))
        })?;

        Ok(providers)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_search_query_builder() {
        let query = SearchQuery::new()
            .with_query("test".to_string())
            .with_pagination(10, 5)
            .with_sort("name:asc".to_string())
            .with_filter("enabled".to_string(), serde_json::Value::Bool(true));

        assert_eq!(query.q, Some("test".to_string()));
        assert_eq!(query.get_size(), 10);
        assert_eq!(query.get_from(), 5);
        assert_eq!(query.sort, Some("name:asc".to_string()));
        assert!(query.filters.is_some());
    }

    #[test]
    fn test_search_response_creation() {
        let providers = vec![];
        let response = SearchResponse::new(providers, 0, 5);

        assert_eq!(response.took, 5);
        assert!(!response.timed_out);
        assert_eq!(response.hits.total.value, 0);
        assert_eq!(response.hits.hits.len(), 0);
    }
}
