use std::{
    path::{Path, PathBuf},
    sync::{Arc, Mutex},
    time::{Duration, SystemTime},
};

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use tokio::time::sleep;
use tracing::{debug, error, info, warn};

use crate::{
    config::config_manager::Config<PERSON>anager,
    error::{error::CocoError, result::Result},
    services::file_watcher::FileWatcher,
};

/// 配置重载状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ReloadStatus {
    /// 空闲状态
    Idle,
    /// 重载中
    Reloading,
    /// 重载成功
    Success,
    /// 重载失败
    Failed(String),
    /// 验证失败，已回滚
    RolledBack(String),
}

/// 配置重载统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReloadStats {
    /// 总重载次数
    pub total_reloads: u64,
    /// 成功重载次数
    pub successful_reloads: u64,
    /// 失败重载次数
    pub failed_reloads: u64,
    /// 回滚次数
    pub rollback_count: u64,
    /// 最后重载时间
    pub last_reload_time: Option<DateTime<Utc>>,
    /// 最后成功重载时间
    pub last_successful_reload: Option<DateTime<Utc>>,
    /// 平均重载耗时（毫秒）
    pub average_reload_time_ms: f64,
}

impl Default for ReloadStats {
    fn default() -> Self {
        Self {
            total_reloads: 0,
            successful_reloads: 0,
            failed_reloads: 0,
            rollback_count: 0,
            last_reload_time: None,
            last_successful_reload: None,
            average_reload_time_ms: 0.0,
        }
    }
}

/// 配置重载服务
#[derive(Clone)]
pub struct ConfigReloadService {
    /// 配置管理器
    config_manager: Arc<Mutex<ConfigManager>>,
    /// 文件监控器
    file_watcher: Arc<Mutex<Option<FileWatcher>>>,
    /// 当前状态
    status: Arc<Mutex<ReloadStatus>>,
    /// 统计信息
    stats: Arc<Mutex<ReloadStats>>,
    /// 配置文件路径
    config_path: PathBuf,
    /// 备份配置路径
    backup_path: PathBuf,
    /// 是否启用自动重载
    auto_reload_enabled: Arc<Mutex<bool>>,
    /// 重载延迟（防抖）
    reload_delay: Duration,
}

impl ConfigReloadService {
    /// 创建新的配置重载服务
    pub fn new<P: AsRef<Path>>(
        config_manager: Arc<Mutex<ConfigManager>>,
        config_path: P,
    ) -> Result<Self> {
        let config_path = config_path.as_ref().to_path_buf();
        let backup_path = config_path.with_extension("backup");

        Ok(Self {
            config_manager,
            file_watcher: Arc::new(Mutex::new(None)),
            status: Arc::new(Mutex::new(ReloadStatus::Idle)),
            stats: Arc::new(Mutex::new(ReloadStats::default())),
            config_path,
            backup_path,
            auto_reload_enabled: Arc::new(Mutex::new(false)),
            reload_delay: Duration::from_millis(500), // 500ms防抖延迟
        })
    }

    /// 启动配置热重载监控
    pub async fn start_hot_reload(&self) -> Result<()> {
        let mut auto_reload = self.auto_reload_enabled.lock().unwrap();
        if *auto_reload {
            warn!("配置热重载已经启动");
            return Ok(());
        }

        // 创建文件监控器
        let file_watcher = FileWatcher::new(&self.config_path, {
            let service = self.clone();
            move |_path| {
                let service = service.clone();
                tokio::spawn(async move {
                    // 防抖延迟
                    sleep(service.reload_delay).await;
                    if let Err(e) = service.reload_config().await {
                        error!("自动重载配置失败: {}", e);
                    }
                });
            }
        })?;

        // 保存文件监控器
        {
            let mut watcher = self.file_watcher.lock().unwrap();
            *watcher = Some(file_watcher);
        }

        *auto_reload = true;
        info!("配置热重载监控已启动，监控文件: {:?}", self.config_path);
        Ok(())
    }

    /// 停止配置热重载监控
    pub fn stop_hot_reload(&self) {
        let mut auto_reload = self.auto_reload_enabled.lock().unwrap();
        if !*auto_reload {
            return;
        }

        // 停止文件监控器
        {
            let mut watcher = self.file_watcher.lock().unwrap();
            *watcher = None;
        }

        *auto_reload = false;
        info!("配置热重载监控已停止");
    }

    /// 手动重载配置
    pub async fn reload_config(&self) -> Result<()> {
        let start_time = SystemTime::now();

        // 设置状态为重载中
        {
            let mut status = self.status.lock().unwrap();
            *status = ReloadStatus::Reloading;
        }

        info!("开始重载配置文件: {:?}", self.config_path);

        // 更新统计信息
        {
            let mut stats = self.stats.lock().unwrap();
            stats.total_reloads += 1;
            stats.last_reload_time = Some(Utc::now());
        }

        // 执行重载
        let result = self.perform_reload().await;

        // 计算耗时
        let elapsed = start_time.elapsed().unwrap_or_default();
        let elapsed_ms = elapsed.as_secs_f64() * 1000.0;

        // 更新状态和统计信息
        match &result {
            Ok(_) => {
                let mut status = self.status.lock().unwrap();
                *status = ReloadStatus::Success;

                let mut stats = self.stats.lock().unwrap();
                stats.successful_reloads += 1;
                stats.last_successful_reload = Some(Utc::now());

                // 更新平均耗时
                let total_time = stats.average_reload_time_ms
                    * (stats.successful_reloads - 1) as f64
                    + elapsed_ms;
                stats.average_reload_time_ms = total_time / stats.successful_reloads as f64;

                info!("配置重载成功，耗时: {}ms", elapsed_ms);
            }
            Err(e) => {
                let error_msg = e.to_string();

                // 只有在状态不是RolledBack时才设置为Failed
                // 如果已经是RolledBack，说明回滚成功，保持该状态
                {
                    let mut status = self.status.lock().unwrap();
                    if !matches!(*status, ReloadStatus::RolledBack(_)) {
                        *status = ReloadStatus::Failed(error_msg.clone());
                    }
                }

                let mut stats = self.stats.lock().unwrap();
                stats.failed_reloads += 1;

                error!("配置重载失败: {}, 耗时: {}ms", error_msg, elapsed_ms);
            }
        }

        result
    }

    /// 执行实际的重载操作
    async fn perform_reload(&self) -> Result<()> {
        // 1. 验证新配置
        let validation_result = self.validate_new_config().await;
        if let Err(e) = validation_result {
            warn!("新配置验证失败，准备回滚: {}", e);
            // 执行回滚并设置状态
            if let Err(rollback_err) = self.rollback_config() {
                error!("回滚失败: {}", rollback_err);
                // 如果回滚失败，设置失败状态
                let mut status = self.status.lock().unwrap();
                *status = ReloadStatus::Failed(format!("配置验证失败且回滚失败: {}", e));
            }
            // 注意：如果回滚成功，状态已经在rollback_config中设置为RolledBack
            return Err(e);
        }

        // 2. 重载配置管理器
        {
            let mut config_manager = self.config_manager.lock().unwrap();
            if let Err(e) = config_manager.manual_reload() {
                warn!("配置管理器重载失败，准备回滚: {}", e);
                if let Err(rollback_err) = self.rollback_config() {
                    error!("回滚失败: {}", rollback_err);
                    let mut status = self.status.lock().unwrap();
                    *status = ReloadStatus::Failed(format!("配置管理器重载失败且回滚失败: {}", e));
                }
                return Err(e);
            }
        }

        // 3. 验证重载后的配置
        if let Err(e) = self.validate_reloaded_config().await {
            warn!("重载后配置验证失败，准备回滚: {}", e);
            if let Err(rollback_err) = self.rollback_config() {
                error!("回滚失败: {}", rollback_err);
                let mut status = self.status.lock().unwrap();
                *status = ReloadStatus::Failed(format!("重载后验证失败且回滚失败: {}", e));
            }
            return Err(e);
        }

        // 4. 备份成功的配置（只有在所有验证都通过后才备份）
        self.backup_current_config()?;

        debug!("配置重载完成");
        Ok(())
    }

    /// 备份当前配置
    fn backup_current_config(&self) -> Result<()> {
        if self.config_path.exists() {
            std::fs::copy(&self.config_path, &self.backup_path)
                .map_err(|e| CocoError::ConfigError(format!("备份配置文件失败: {}", e)))?;
            debug!("配置文件已备份到: {:?}", self.backup_path);
        }
        Ok(())
    }

    /// 验证新配置文件
    async fn validate_new_config(&self) -> Result<()> {
        // 检查文件是否存在
        if !self.config_path.exists() {
            return Err(CocoError::ConfigError("配置文件不存在".to_string()));
        }

        // 尝试解析配置文件
        let config_content = std::fs::read_to_string(&self.config_path)
            .map_err(|e| CocoError::ConfigError(format!("读取配置文件失败: {}", e)))?;

        // 验证YAML格式
        serde_yaml::from_str::<serde_yaml::Value>(&config_content)
            .map_err(|e| CocoError::ConfigError(format!("配置文件格式错误: {}", e)))?;

        debug!("新配置文件验证通过");
        Ok(())
    }

    /// 验证重载后的配置
    async fn validate_reloaded_config(&self) -> Result<()> {
        let config_manager = self.config_manager.lock().unwrap();

        // 验证端口配置
        config_manager.validate_port_config()?;

        // 验证数据库配置
        config_manager.validate_database_config()?;

        // 验证TOML配置
        config_manager.validate_toml_config()?;

        debug!("重载后配置验证通过");
        Ok(())
    }

    /// 回滚配置
    fn rollback_config(&self) -> Result<()> {
        if self.backup_path.exists() {
            std::fs::copy(&self.backup_path, &self.config_path)
                .map_err(|e| CocoError::ConfigError(format!("回滚配置文件失败: {}", e)))?;

            // 重新加载配置管理器以使用回滚的配置
            {
                let mut config_manager = self.config_manager.lock().unwrap();
                let _ = config_manager.manual_reload(); // 忽略错误，因为我们已经在错误处理中
            }

            // 更新状态
            {
                let mut status = self.status.lock().unwrap();
                *status = ReloadStatus::RolledBack("配置验证失败，已回滚到备份版本".to_string());
            }

            // 更新统计信息
            {
                let mut stats = self.stats.lock().unwrap();
                stats.rollback_count += 1;
            }

            warn!("配置已回滚到备份版本: {:?}", self.backup_path);
        } else {
            warn!("备份文件不存在，无法回滚");
        }
        Ok(())
    }

    /// 获取当前状态
    pub fn get_status(&self) -> ReloadStatus {
        self.status.lock().unwrap().clone()
    }

    /// 获取统计信息
    pub fn get_stats(&self) -> ReloadStats {
        self.stats.lock().unwrap().clone()
    }

    /// 检查是否启用了自动重载
    pub fn is_auto_reload_enabled(&self) -> bool {
        *self.auto_reload_enabled.lock().unwrap()
    }

    /// 获取配置文件路径
    pub fn get_config_path(&self) -> &Path {
        &self.config_path
    }

    /// 清理备份文件
    pub fn cleanup_backup(&self) -> Result<()> {
        if self.backup_path.exists() {
            std::fs::remove_file(&self.backup_path)
                .map_err(|e| CocoError::ConfigError(format!("清理备份文件失败: {}", e)))?;
            debug!("备份文件已清理: {:?}", self.backup_path);
        }
        Ok(())
    }
}
