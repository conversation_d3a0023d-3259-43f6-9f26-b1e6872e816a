/// 错误处理系统测试
///
/// 测试TASK-004实现的分层错误处理系统，包括错误转换、HTTP响应格式和日志记录。
use axum::{http::StatusCode, response::IntoResponse};
use coco_server::error::{
    error::CocoError, ApiError, AuthError, ErrorResponse, RepositoryError, ServiceError,
};

/// 测试RepositoryError的创建和属性
#[test]
fn test_repository_error_creation() {
    // 测试数据库错误
    let db_error = RepositoryError::database("Connection failed", None::<std::io::Error>);
    assert_eq!(db_error.error_type(), "database");
    assert!(db_error.is_retryable());

    // 测试未找到错误
    let not_found_error = RepositoryError::not_found("ModelProvider", "test-id");
    assert_eq!(not_found_error.error_type(), "not_found");
    assert!(!not_found_error.is_retryable());

    // 测试冲突错误
    let conflict_error = RepositoryError::conflict(
        "Name already exists",
        Some("name".to_string()),
        Some("duplicate-name".to_string()),
    );
    assert_eq!(conflict_error.error_type(), "conflict");
    assert!(!conflict_error.is_retryable());
}

/// 测试ServiceError的创建和转换
#[test]
fn test_service_error_creation_and_conversion() {
    // 测试业务逻辑错误
    let business_error = ServiceError::business_logic(
        "Invalid operation",
        Some("OP001".to_string()),
        Some("User context".to_string()),
    );
    assert_eq!(business_error.error_type(), "business_logic");
    assert!(!business_error.is_retryable());
    assert!(business_error.is_client_error());

    // 测试从RepositoryError转换
    let repo_error = RepositoryError::database("DB connection failed", None::<std::io::Error>);
    let service_error: ServiceError = repo_error.into();
    assert_eq!(service_error.error_type(), "repository");
    assert!(service_error.is_retryable());

    // 测试验证错误
    let validation_error = ServiceError::validation(
        "Invalid email format",
        Some("email".to_string()),
        Some("invalid@".to_string()),
    );
    assert!(validation_error.is_client_error());
}

/// 测试AuthError的创建和分类
#[test]
fn test_auth_error_creation_and_classification() {
    // 测试无效令牌错误
    let invalid_token = AuthError::invalid_token("Malformed JWT");
    assert_eq!(invalid_token.error_type(), "invalid_token");
    assert!(invalid_token.is_client_error());
    assert!(invalid_token.requires_reauth());

    // 测试权限不足错误
    let permission_denied =
        AuthError::permission_denied("admin", Some(vec!["user".to_string(), "read".to_string()]));
    assert_eq!(permission_denied.error_type(), "permission_denied");
    assert!(permission_denied.is_client_error());
    assert!(!permission_denied.requires_reauth());

    // 测试令牌过期
    let token_expired = AuthError::TokenExpired;
    assert!(token_expired.is_client_error());
    assert!(token_expired.requires_reauth());

    // 测试配置错误
    let config_error =
        AuthError::configuration_error("Missing JWT secret", Some("jwt.secret".to_string()));
    assert!(!config_error.is_client_error());
    assert!(!config_error.requires_reauth());
}

/// 测试ApiError的创建和状态码映射
#[test]
fn test_api_error_creation_and_status_codes() {
    // 测试验证错误
    let validation_error = ApiError::validation(
        "Invalid input",
        Some("email".to_string()),
        Some("invalid@".to_string()),
    );
    assert_eq!(validation_error.status_code(), StatusCode::BAD_REQUEST);
    assert_eq!(validation_error.error_type(), "validation");

    // 测试未找到错误
    let not_found_error = ApiError::not_found("User", Some("123".to_string()));
    assert_eq!(not_found_error.status_code(), StatusCode::NOT_FOUND);

    // 测试冲突错误
    let conflict_error = ApiError::conflict("Resource already exists", Some("User".to_string()));
    assert_eq!(conflict_error.status_code(), StatusCode::CONFLICT);

    // 测试请求过于频繁
    let rate_limit_error = ApiError::too_many_requests(Some(60));
    assert_eq!(
        rate_limit_error.status_code(),
        StatusCode::TOO_MANY_REQUESTS
    );
}

/// 测试错误转换链
#[test]
fn test_error_conversion_chain() {
    // Repository -> Service -> Api 转换链
    let repo_error = RepositoryError::not_found("ModelProvider", "test-id");
    let service_error: ServiceError = repo_error.into();
    let api_error: ApiError = service_error.into();

    assert!(matches!(api_error, ApiError::Service(_)));
    assert_eq!(api_error.status_code(), StatusCode::NOT_FOUND);

    // Auth -> Api 转换
    let auth_error = AuthError::TokenExpired;
    let api_error: ApiError = auth_error.into();

    assert!(matches!(api_error, ApiError::Auth(_)));
    assert_eq!(api_error.status_code(), StatusCode::UNAUTHORIZED);
}

/// 测试CocoError集成
#[test]
fn test_coco_error_integration() {
    // 测试新错误类型的集成
    let api_error = ApiError::validation("Invalid data", None, None);
    let coco_error: CocoError = api_error.into();

    assert!(matches!(coco_error, CocoError::Api(_)));

    // 测试服务错误集成
    let service_error = ServiceError::business_logic("Invalid operation", None, None);
    let coco_error: CocoError = service_error.into();

    assert!(matches!(coco_error, CocoError::Service(_)));

    // 测试认证错误集成
    let auth_error = AuthError::InvalidCredentials;
    let coco_error: CocoError = auth_error.into();

    assert!(matches!(coco_error, CocoError::Auth(_)));
}

/// 测试ErrorResponse格式
#[test]
fn test_error_response_format() {
    // 测试验证错误响应
    let api_error = ApiError::validation(
        "Invalid email format",
        Some("email".to_string()),
        Some("invalid@".to_string()),
    );
    let response = ErrorResponse::from_api_error(&api_error);

    assert_eq!(response.error, "请求验证失败");
    assert_eq!(response.code, 400);
    assert_eq!(response.error_type, "validation");
    assert!(response.details.is_some());

    let details = response.details.unwrap();
    assert_eq!(details.field, Some("email".to_string()));
    assert_eq!(details.value, Some("invalid@".to_string()));

    // 测试认证错误响应
    let auth_error = AuthError::TokenExpired;
    let api_error = ApiError::Auth(auth_error);
    let response = ErrorResponse::from_api_error(&api_error);

    assert_eq!(response.error, "令牌已过期");
    assert_eq!(response.code, 401);
    assert_eq!(response.error_type, "auth");

    // 测试简单错误响应
    let simple_response = ErrorResponse::simple("Test error", 500);
    assert_eq!(simple_response.error, "Test error");
    assert_eq!(simple_response.code, 500);
    assert_eq!(simple_response.error_type, "unknown");
}

/// 测试HTTP响应转换
#[tokio::test]
async fn test_http_response_conversion() {
    // 测试ApiError转换为HTTP响应
    let api_error = ApiError::validation("Invalid input", None, None);
    let response = api_error.into_response();

    assert_eq!(response.status(), StatusCode::BAD_REQUEST);

    // 测试CocoError转换为HTTP响应
    let auth_error = AuthError::InvalidCredentials;
    let coco_error = CocoError::Auth(auth_error);
    let response = coco_error.into_response();

    assert_eq!(response.status(), StatusCode::UNAUTHORIZED);

    // 测试传统CocoError的兼容性
    let legacy_error = CocoError::not_found("Resource not found");
    let response = legacy_error.into_response();

    assert_eq!(response.status(), StatusCode::NOT_FOUND);
}

/// 测试错误序列化
#[test]
fn test_error_serialization() {
    let api_error = ApiError::not_found("User", Some("123".to_string()));
    let error_response = ErrorResponse::from_api_error(&api_error);

    // 测试序列化为JSON
    let json_result = serde_json::to_value(&error_response);
    assert!(json_result.is_ok());

    let json_value = json_result.unwrap();
    assert!(json_value.is_object());

    let obj = json_value.as_object().unwrap();
    assert!(obj.contains_key("error"));
    assert!(obj.contains_key("code"));
    assert!(obj.contains_key("error_type"));
    assert!(obj.contains_key("timestamp"));
    assert!(obj.contains_key("trace_id"));
}

/// 测试错误反序列化
#[test]
fn test_error_deserialization() {
    let json_str = r#"{
        "error": "Test error",
        "code": 400,
        "error_type": "validation",
        "timestamp": "2023-01-01T00:00:00Z",
        "trace_id": "test-trace-id"
    }"#;

    let result: Result<ErrorResponse, _> = serde_json::from_str(json_str);
    assert!(result.is_ok());

    let error_response = result.unwrap();
    assert_eq!(error_response.error, "Test error");
    assert_eq!(error_response.code, 400);
    assert_eq!(error_response.error_type, "validation");
}

/// 测试错误链追踪
#[test]
fn test_error_chain_tracing() {
    // 创建一个复杂的错误链
    let repo_error = RepositoryError::database("Connection timeout", None::<std::io::Error>);
    let service_error = ServiceError::Repository(repo_error);
    let api_error = ApiError::Service(service_error);
    let coco_error = CocoError::Api(api_error);

    // 验证错误链可以正确追踪
    match coco_error {
        CocoError::Api(ApiError::Service(ServiceError::Repository(repo_err))) => {
            assert_eq!(repo_err.error_type(), "database");
        }
        _ => panic!("错误链结构不正确"),
    }
}
