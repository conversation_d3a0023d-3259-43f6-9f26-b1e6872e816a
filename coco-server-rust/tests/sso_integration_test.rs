use std::sync::Arc;

use axum::{
    body::Body,
    http::{Request, StatusCode},
    Router,
};
// 导入必要的模块
use coco_server::config::config_manager::ConfigManager;
use coco_server::handlers::sso_handler::sso_login_handler;
use http_body_util::BodyExt;
use tower::ServiceExt;

/// 创建测试路由器
fn create_test_router() -> Router {
    let config_manager = Arc::new(ConfigManager::new().unwrap());

    Router::new()
        .route("/sso/login/cloud", axum::routing::get(sso_login_handler))
        .with_state(config_manager)
}

#[tokio::test]
async fn test_sso_login_with_all_parameters() {
    let app = create_test_router();

    let request = Request::builder()
        .method("GET")
        .uri("/sso/login/cloud?provider=coco-cloud&product=coco&request_id=test123")
        .body(Body::empty())
        .unwrap();

    let response = app.oneshot(request).await.unwrap();

    // 验证响应状态码
    assert_eq!(response.status(), StatusCode::OK);

    // 验证响应头
    let content_type = response.headers().get("content-type").unwrap();
    assert!(content_type.to_str().unwrap().contains("text/html"));

    // 读取响应体
    let body = response.into_body().collect().await.unwrap().to_bytes();
    let html_content = String::from_utf8(body.to_vec()).unwrap();

    // 验证HTML内容包含必要的元素
    assert!(html_content.contains("<!DOCTYPE html>"));
    assert!(html_content.contains("登录成功！"));
    assert!(html_content.contains("正在重定向到 Coco AI 应用..."));
    assert!(html_content.contains("cocoai://oauth_callback"));
    assert!(html_content.contains("request_id=test123"));
    assert!(html_content.contains("provider=coco-cloud"));
    assert!(html_content.contains("code="));
    assert!(html_content.contains("expire_in="));

    // 验证重定向URL格式
    assert!(html_content.contains("cocoai://oauth_callback?code="));

    // 验证JavaScript倒计时功能
    assert!(html_content.contains("let countdown = 5"));
    assert!(html_content.contains("setInterval"));
}

#[tokio::test]
async fn test_sso_login_with_default_parameters() {
    let app = create_test_router();

    let request = Request::builder()
        .method("GET")
        .uri("/sso/login/cloud")
        .body(Body::empty())
        .unwrap();

    let response = app.oneshot(request).await.unwrap();

    // 验证响应状态码
    assert_eq!(response.status(), StatusCode::OK);

    // 读取响应体
    let body = response.into_body().collect().await.unwrap().to_bytes();
    let html_content = String::from_utf8(body.to_vec()).unwrap();

    // 验证默认参数
    assert!(html_content.contains("provider=coco-cloud")); // 默认provider
    assert!(html_content.contains("cocoai://oauth_callback"));

    // 验证包含UUID格式的request_id（36个字符，包含4个连字符）
    let uuid_pattern = regex::Regex::new(
        r"request_id=[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}",
    )
    .unwrap();
    assert!(uuid_pattern.is_match(&html_content));
}

#[tokio::test]
async fn test_sso_login_with_partial_parameters() {
    let app = create_test_router();

    let request = Request::builder()
        .method("GET")
        .uri("/sso/login/cloud?provider=custom-provider&request_id=custom123")
        .body(Body::empty())
        .unwrap();

    let response = app.oneshot(request).await.unwrap();

    // 验证响应状态码
    assert_eq!(response.status(), StatusCode::OK);

    // 读取响应体
    let body = response.into_body().collect().await.unwrap().to_bytes();
    let html_content = String::from_utf8(body.to_vec()).unwrap();

    // 验证自定义参数
    assert!(html_content.contains("provider=custom-provider"));
    assert!(html_content.contains("request_id=custom123"));
    assert!(html_content.contains("cocoai://oauth_callback"));
}

#[tokio::test]
async fn test_sso_login_url_encoding() {
    let app = create_test_router();

    // 测试URL编码的参数
    let request = Request::builder()
        .method("GET")
        .uri("/sso/login/cloud?provider=test%20provider&request_id=test%2Bid")
        .body(Body::empty())
        .unwrap();

    let response = app.oneshot(request).await.unwrap();

    // 验证响应状态码
    assert_eq!(response.status(), StatusCode::OK);

    // 读取响应体
    let body = response.into_body().collect().await.unwrap().to_bytes();
    let html_content = String::from_utf8(body.to_vec()).unwrap();

    // 验证URL解码后的参数
    assert!(
        html_content.contains("provider=test%20provider")
            || html_content.contains("provider=test provider")
    );
    assert!(
        html_content.contains("request_id=test%2Bid")
            || html_content.contains("request_id=test+id")
    );
}

#[tokio::test]
async fn test_sso_login_jwt_token_format() {
    let app = create_test_router();

    let request = Request::builder()
        .method("GET")
        .uri("/sso/login/cloud?request_id=jwt_test")
        .body(Body::empty())
        .unwrap();

    let response = app.oneshot(request).await.unwrap();

    // 验证响应状态码
    assert_eq!(response.status(), StatusCode::OK);

    // 读取响应体
    let body = response.into_body().collect().await.unwrap().to_bytes();
    let html_content = String::from_utf8(body.to_vec()).unwrap();

    // 提取JWT令牌
    let jwt_pattern = regex::Regex::new(r"code=([^&]+)").unwrap();
    if let Some(captures) = jwt_pattern.captures(&html_content) {
        let jwt_token = &captures[1];

        // 验证JWT格式（三个部分，用.分隔）
        let parts: Vec<&str> = jwt_token.split('.').collect();
        assert_eq!(parts.len(), 3, "JWT应该有三个部分");

        // 验证每个部分都是base64编码
        for part in parts {
            assert!(!part.is_empty(), "JWT部分不应为空");
            // 基本的base64字符检查
            assert!(
                part.chars()
                    .all(|c| c.is_alphanumeric() || c == '-' || c == '_'),
                "JWT部分应该是有效的base64编码"
            );
        }
    } else {
        panic!("未找到JWT令牌");
    }
}

#[tokio::test]
async fn test_sso_login_html_structure() {
    let app = create_test_router();

    let request = Request::builder()
        .method("GET")
        .uri("/sso/login/cloud")
        .body(Body::empty())
        .unwrap();

    let response = app.oneshot(request).await.unwrap();

    // 读取响应体
    let body = response.into_body().collect().await.unwrap().to_bytes();
    let html_content = String::from_utf8(body.to_vec()).unwrap();

    // 验证HTML结构
    assert!(html_content.contains("<!DOCTYPE html>"));
    assert!(html_content.contains("<html lang=\"zh-CN\">"));
    assert!(html_content.contains("<head>"));
    assert!(html_content.contains("<title>Coco AI - 登录成功</title>"));
    assert!(html_content.contains("<meta charset=\"UTF-8\">"));
    assert!(html_content.contains("<style>"));
    assert!(html_content.contains("<body>"));
    assert!(html_content.contains("<script>"));

    // 验证关键元素
    assert!(html_content.contains("class=\"container\""));
    assert!(html_content.contains("class=\"success-icon\""));
    assert!(html_content.contains("class=\"manual-link\""));
    assert!(html_content.contains("class=\"copy-url\""));
    assert!(html_content.contains("id=\"countdown\""));
    assert!(html_content.contains("id=\"redirect-link\""));
    assert!(html_content.contains("id=\"copy-url\""));
}

#[tokio::test]
async fn test_sso_login_method_not_allowed() {
    let app = create_test_router();

    // 测试POST方法（应该不被允许）
    let request = Request::builder()
        .method("POST")
        .uri("/sso/login/cloud")
        .body(Body::empty())
        .unwrap();

    let response = app.oneshot(request).await.unwrap();

    // 验证响应状态码应该是405 Method Not Allowed
    assert_eq!(response.status(), StatusCode::METHOD_NOT_ALLOWED);
}

#[tokio::test]
async fn test_sso_login_redirect_url_components() {
    let app = create_test_router();

    let request = Request::builder()
        .method("GET")
        .uri("/sso/login/cloud?provider=test-provider&request_id=test-request")
        .body(Body::empty())
        .unwrap();

    let response = app.oneshot(request).await.unwrap();

    // 读取响应体
    let body = response.into_body().collect().await.unwrap().to_bytes();
    let html_content = String::from_utf8(body.to_vec()).unwrap();

    // 验证重定向URL包含所有必需的组件
    assert!(html_content.contains("cocoai://oauth_callback?"));
    assert!(html_content.contains("code="));
    assert!(html_content.contains("request_id=test-request"));
    assert!(html_content.contains("provider=test-provider"));
    assert!(html_content.contains("expire_in="));

    // 验证参数顺序（应该是code, request_id, provider, expire_in）
    let url_pattern = regex::Regex::new(r"cocoai://oauth_callback\?code=([^&]+)&request_id=([^&]+)&provider=([^&]+)&expire_in=(\d+)").unwrap();
    assert!(url_pattern.is_match(&html_content), "重定向URL格式不正确");
}
